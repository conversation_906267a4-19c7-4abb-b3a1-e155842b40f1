package com.bearmod;

import android.content.Context;
import android.content.SharedPreferences;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class ConfigurationManager {
    private static final String CONFIG_FILE = "NRG_SaveFile.cfg";
    private static final String PREFS_NAME = "config";
    
    private final Context context;
    private final Map<String, String> configMap;
    private final SharedPreferences prefs;
    private final ConfigurationCallback callback;

    public interface ConfigurationCallback {
        void onConfigChanged(String key, String value);
    }

    public ConfigurationManager(Context context, ConfigurationCallback callback) {
        this.context = context;
        this.configMap = new HashMap<>();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.callback = callback;
        loadConfiguration();
    }

    public void loadConfiguration() {
        try {
            File file = new File(context.getFilesDir(), CONFIG_FILE);
            if (!file.exists()) {
                initDefaultConfigurations();
                return;
            }

            BufferedReader reader = new BufferedReader(new FileReader(file));
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(" = ");
                if (parts.length == 2) {
                    configMap.put(parts[0], parts[1]);
                }
            }
            reader.close();
        } catch (Exception e) {
            android.util.Log.e("ConfigurationManager", "Error in loadConfiguration", e);
        }
    }

    public void saveConfiguration() {
        try {
            File file = new File(context.getFilesDir(), CONFIG_FILE);
            PrintWriter writer = new PrintWriter(new FileOutputStream(file), true);
            
            for (Map.Entry<String, String> entry : configMap.entrySet()) {
                writer.println(entry.getKey() + " = " + entry.getValue());
            }
            writer.close();
        } catch (Exception e) {
            android.util.Log.e("ConfigurationManager", "Error in saveConfiguration", e);
        }
    }

    public void updateConfig(String key, Object value) {
        String stringValue = value.toString();
        configMap.put(key, stringValue);
        
        // Update SharedPreferences
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(key, stringValue);
        editor.apply();
        
        // Notify callback
        if (callback != null) {
            callback.onConfigChanged(key, stringValue);
        }
    }

    public boolean getBoolean(String key, boolean defaultValue) {
        String value = configMap.get(key);
        return value != null ? Integer.parseInt(value) == 1 : defaultValue;
    }

    public int getInteger(String key, int defaultValue) {
        String value = configMap.get(key);
        return value != null ? Integer.parseInt(value) : defaultValue;
    }

    public String getString(String key, String defaultValue) {
        return configMap.getOrDefault(key, defaultValue);
    }

    private void initDefaultConfigurations() {
        updateConfig("AIM::TRIGGER1", 1);
        updateConfig("AIM::TARGET1", 1);
        updateConfig("ESP::BOXTYPE1", 1);
        updateConfig("AIM_MOD1", 1);
        updateConfig("SMOOT::HNESS1", 1);
        updateConfig("RADAR::SIZE", 60);
        saveConfiguration();
    }

    public void clearConfig() {
        configMap.clear();
        prefs.edit().clear().apply();
        initDefaultConfigurations();
    }
} 