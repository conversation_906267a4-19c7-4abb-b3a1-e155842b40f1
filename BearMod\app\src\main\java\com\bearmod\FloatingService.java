package com.bearmod;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.IBinder;
import android.view.Gravity;
import android.view.WindowManager;
import android.view.Display;
import android.graphics.Point;

public abstract class FloatingService extends Service {
    protected WindowManager windowManager;
    protected WindowManager.LayoutParams mainLayoutParams;
    protected int screenWidth, screenHeight;
    protected int layoutWidth, layoutHeight;
    protected int type;

    @Override
    public void onCreate() {
        super.onCreate();
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        initializeWindowMetrics();
    }

    protected void initializeWindowMetrics() {
        Display display = windowManager.getDefaultDisplay();
        Point size = new Point();
        display.getRealSize(size);
        screenWidth = size.x;
        screenHeight = size.y;
        
        // Set default layout parameters
        layoutWidth = dpToPx(300);  // Fixed width in dp
        layoutHeight = dpToPx(200); // Fixed height in dp
        type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        
        mainLayoutParams = new WindowManager.LayoutParams(
            layoutWidth,
            layoutHeight,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        );
        mainLayoutParams.gravity = Gravity.START | Gravity.TOP;
    }

    protected int dpToPx(int dp) {
        return Math.round(dp * getResources().getDisplayMetrics().density);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cleanup();
    }

    protected abstract void cleanup();
}