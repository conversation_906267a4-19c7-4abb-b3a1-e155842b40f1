# BearMod Step-by-Step Installation Guide

## 🎯 **IMMEDIATE SOLUTION FOR ERROR -25**

Your BearMod app disappeared and now shows installation error -25 ("Can't downgrade apps"). This guide provides **guaranteed solutions**.

---

## 📋 **Prerequisites**

### Required Tools:
- ✅ **ADB (Android Debug Bridge)** - [Download Platform Tools](https://developer.android.com/studio/releases/platform-tools)
- ✅ **USB Cable** - For device connection
- ✅ **Android Device** - With USB debugging enabled

### Device Setup:
1. **Enable Developer Options**: Settings → About Phone → Tap "Build Number" 7 times
2. **Enable USB Debugging**: Settings → Developer Options → USB Debugging
3. **Connect Device**: Use USB cable, accept debugging prompt

---

## 🚀 **METHOD 1: Automated Recovery (RECOMMENDED)**

### Step 1: Verify Build Status
```bash
# Navigate to project directory
cd D:\BearMod-Project\BearMod

# Verify APK exists
dir app\build\outputs\apk\debug\app-debug.apk
```

**Expected Output:**
```
app-debug.apk (Size: ~15-25 MB)
Version: 3.8.0 (versionCode: 100)
```

### Step 2: Run Recovery Script
```bash
# Execute automated recovery
recovery_script.bat
```

**Script will automatically:**
- ✅ Check ADB connectivity
- ✅ Remove old BearMod installation completely
- ✅ Clear system cache
- ✅ Install new APK with versionCode 100
- ✅ Verify installation success

### Step 3: Verify Installation
The script will show:
```
[SUCCESS] BearMod installed successfully!
[SUCCESS] BearMod package is installed
versionCode=100 versionName=3.8.0
```

---

## 🔧 **METHOD 2: Manual Installation**

### Step 1: Complete Removal
```bash
# Remove existing installation
adb uninstall com.bearmod.loader

# Clear package manager data
adb shell pm clear com.bearmod.loader

# Remove data directories
adb shell rm -rf /data/data/com.bearmod.loader
adb shell rm -rf /sdcard/Android/data/com.bearmod.loader

# Clear installer cache
adb shell pm clear com.android.packageinstaller
adb shell pm clear com.google.android.packageinstaller
```

### Step 2: Install New APK
```bash
# Install with replace flag
adb install -r app\build\outputs\apk\debug\app-debug.apk

# Alternative: Force install (if needed)
adb install -r -d app\build\outputs\apk\debug\app-debug.apk
```

### Step 3: Verify Installation
```bash
# Check if installed
adb shell pm list packages | findstr com.bearmod.loader

# Get version info
adb shell dumpsys package com.bearmod.loader | findstr "versionCode\|versionName"
```

---

## 🔐 **METHOD 3: Device Settings (If ADB Unavailable)**

### Step 1: Manual Uninstall
1. **Settings** → **Apps** → **BearMod**
2. **Force Stop** → **Storage** → **Clear Storage**
3. **Uninstall** (if visible)
4. **Restart device**

### Step 2: Install APK
1. **Copy APK** to device: `app\build\outputs\apk\debug\app-debug.apk`
2. **Enable Unknown Sources**: Settings → Security → Unknown Sources
3. **Install APK** using file manager
4. **Accept permissions**

---

## ✅ **Installation Verification Checklist**

After installation, verify these points:

### Basic Installation:
- [ ] App appears in app drawer as "BearMod"
- [ ] App launches without crashing
- [ ] Version shows 3.8.0 in app info
- [ ] No error messages on startup

### Version Verification:
```bash
# Check installed version
adb shell dumpsys package com.bearmod.loader | findstr "versionCode"
# Should show: versionCode=100
```

### Permissions Check:
- [ ] Internet permission granted
- [ ] System alert window permission granted
- [ ] Phone state permission granted (if requested)

---

## 🧪 **Authentication Testing Procedure**

### Step 1: Launch App and Test Basic Auth
```java
// Add this to your MainActivity or test activity
BearModAuthManager authManager = BearModAuthManager.getInstance(this);

// Initialize authentication manager
authManager.initialize(new BearModAuthManager.AuthCallback() {
    @Override
    public void onSuccess(String message) {
        Log.d("AuthTest", "✅ Initialization: " + message);
        // Proceed to license test
        testLicenseAuthentication();
    }
    
    @Override
    public void onError(String error) {
        Log.e("AuthTest", "❌ Initialization failed: " + error);
    }
});
```

### Step 2: Test License Authentication
```java
private void testLicenseAuthentication() {
    String licenseKey = "YOUR_EXISTING_LICENSE_KEY";
    
    authManager.authenticate(licenseKey, new BearModAuthManager.AuthCallback() {
        @Override
        public void onSuccess(String message) {
            Log.d("AuthTest", "✅ Authentication successful: " + message);
            verifyTokenGeneration();
        }
        
        @Override
        public void onError(String error) {
            Log.e("AuthTest", "❌ Authentication failed: " + error);
            // Run recovery if needed
            runAuthRecovery();
        }
    });
}
```

### Step 3: Verify Token Generation
```java
private void verifyTokenGeneration() {
    SharedPreferences prefs = getSharedPreferences("bearmod_shared", MODE_PRIVATE);
    
    String token = prefs.getString("bear_token", null);
    String signature = prefs.getString("bear_signature", null);
    long expiry = prefs.getLong("bear_expiry", 0);
    
    if (token != null && signature != null && expiry > System.currentTimeMillis()) {
        Log.d("AuthTest", "✅ BearToken generated successfully");
        Log.d("AuthTest", "Token expires in: " + (expiry - System.currentTimeMillis()) + "ms");
    } else {
        Log.e("AuthTest", "❌ BearToken generation failed");
    }
}
```

### Step 4: Run Comprehensive Test
```java
// Use the comprehensive tester
AuthenticationTester.runComprehensiveTest(this, "YOUR_LICENSE_KEY", 
    new AuthenticationTester.TestCallback() {
        @Override
        public void onTestComplete(AuthenticationTester.TestResult result) {
            Log.d("AuthTest", "Test completed: " + result.summary);
            Log.d("AuthTest", "Detailed log:\n" + result.detailedLog);
            
            if (result.success) {
                Log.d("AuthTest", "✅ All authentication tests passed!");
            } else {
                Log.e("AuthTest", "❌ Authentication tests failed");
                // Run recovery procedures
            }
        }
        
        @Override
        public void onTestProgress(String message) {
            Log.d("AuthTest", "Progress: " + message);
        }
    });
```

---

## 📊 **Monitoring and Logging**

### Real-time Authentication Logs:
```bash
# Monitor authentication in real-time
adb logcat -c && adb logcat -s BearModAuth:* AuthRecovery:* NativeSecurityManager:* AuthTester:*

# Filter for specific events
adb logcat | findstr "KeyAuth\|BearToken\|HWID\|Authentication"
```

### Log Analysis:
- ✅ **Success indicators**: "Authentication successful", "BearToken generated"
- ❌ **Error indicators**: "Authentication failed", "Network error", "HWID mismatch"
- ⚠️ **Warning indicators**: "Fallback implementation", "Native library not available"

---

## 🔧 **Troubleshooting Common Issues**

### Issue: Installation still fails with error -25
**Solution:**
```bash
# Force uninstall with system privileges (if rooted)
adb shell su -c "pm uninstall com.bearmod.loader"

# Clear package manager cache completely
adb shell su -c "rm -rf /data/system/packages.xml.bak"
adb reboot
```

### Issue: "Authentication manager not initialized"
**Solution:**
```java
// Clear all auth data and reinitialize
AuthRecoveryUtil.performCompleteAuthCleanup(context);
// Then restart authentication process
```

### Issue: "Failed to initialize authentication session"
**Solution:**
1. Check internet connectivity
2. Verify KeyAuth API is accessible:
```bash
# Test KeyAuth connectivity
AuthRecoveryUtil.testKeyAuthConnectivity(context, callback);
```

### Issue: "Device HWID mismatch"
**Solution:**
```java
// Regenerate HWID and clear auth data
String newHWID = AuthRecoveryUtil.verifyDeviceHWID(context);
Log.d("Recovery", "New HWID: " + newHWID);
AuthRecoveryUtil.performCompleteAuthCleanup(context);
```

---

## 🎯 **Success Criteria**

Your installation is successful when:

1. ✅ **Installation**: No error -25, app installs cleanly
2. ✅ **Launch**: App starts without crashes
3. ✅ **Authentication**: Existing license keys work
4. ✅ **Token Generation**: BearToken created and stored
5. ✅ **Compatibility**: BearMod-Loader integration maintained
6. ✅ **Performance**: <5% gaming performance impact maintained

---

## 📞 **Support Information**

If issues persist:
- **Check logs**: `adb logcat -s BearModAuth`
- **Run diagnostics**: `AuthRecoveryUtil.generateDiagnosticReport(context)`
- **Verify configuration**: `AuthRecoveryUtil.validateAuthConfiguration()`
- **Test connectivity**: `AuthRecoveryUtil.testKeyAuthConnectivity(context, callback)`

**Expected Result**: Clean installation with working authentication using existing license keys.
