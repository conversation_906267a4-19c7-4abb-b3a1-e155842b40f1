#!/usr/bin/env python3
"""
KeyAuth API Test Utility for BearMod
=====================================

A simple command-line utility to test KeyAuth API integration without requiring
the full Android build. This allows for faster iteration and debugging of
KeyAuth license validation.

Usage:
    python keyauth_test_utility.py --license YOUR_LICENSE_KEY
    python keyauth_test_utility.py --license YOUR_LICENSE_KEY --verbose
    python keyauth_test_utility.py --test-config

Requirements:
    pip install requests

Author: BearMod Development Team
Version: 1.0.0
"""

import argparse
import hashlib
import json
import requests
import sys
import time
from typing import Dict, Optional, Tuple


class KeyAuthTester:
    """KeyAuth API testing utility matching BearMod implementation"""
    
    # KeyAuth Configuration - MUST match BearMod-Loader exactly
    API_URL = "https://enc.mod-key.click/1.2/"
    APP_NAME = "com.bearmod.loader"
    OWNER_ID = "yLoA9zcOEF"
    VERSION = "1.3"
    APP_HASH = "60885ac0cf1061079d5756a689630d13"
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.session_id = None
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        if self.verbose or level == "ERROR":
            print(f"[{timestamp}] {level}: {message}")
    
    def generate_test_hwid(self) -> str:
        """Generate a test HWID for validation"""
        # Simulate Android device identifiers
        test_android_id = "test_android_id_12345"
        test_fingerprint = "test/device/fingerprint:1.0"
        combined = f"{test_android_id}-{test_fingerprint}"
        
        # Hash using SHA-256 and return first 32 characters
        hash_obj = hashlib.sha256(combined.encode('utf-8'))
        hwid = hash_obj.hexdigest()[:32]
        
        self.log(f"Generated test HWID: {hwid}")
        return hwid
    
    def initialize_session(self) -> Tuple[bool, str]:
        """Initialize KeyAuth session"""
        self.log("Initializing KeyAuth session...")
        
        try:
            # Build API URL for initialization
            init_url = (f"{self.API_URL}?type=init&name={self.APP_NAME}"
                       f"&ownerid={self.OWNER_ID}&ver={self.VERSION}"
                       f"&hash={self.APP_HASH}")
            
            self.log(f"Init URL: {init_url}")
            
            # Make GET request
            headers = {"User-Agent": "KeyAuth"}
            response = requests.get(init_url, headers=headers, timeout=10)
            
            self.log(f"Response status: {response.status_code}")
            self.log(f"Response body: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("success", False):
                        self.session_id = data.get("sessionid")
                        self.log(f"Session initialized successfully: {self.session_id}")
                        return True, "Session initialized successfully"
                    else:
                        error_msg = data.get("message", "Unknown error")
                        self.log(f"Session initialization failed: {error_msg}", "ERROR")
                        return False, error_msg
                except json.JSONDecodeError as e:
                    self.log(f"Failed to parse JSON response: {e}", "ERROR")
                    return False, f"Invalid JSON response: {response.text}"
            else:
                self.log(f"HTTP error: {response.status_code}", "ERROR")
                return False, f"HTTP error: {response.status_code}"
                
        except requests.RequestException as e:
            self.log(f"Network error: {e}", "ERROR")
            return False, f"Network error: {e}"
    
    def validate_license(self, license_key: str) -> Tuple[bool, str, Dict]:
        """Validate license key with KeyAuth API"""
        if not self.session_id:
            return False, "Session not initialized", {}
        
        self.log(f"Validating license key: {license_key[:8]}...")
        
        try:
            # Generate test HWID
            hwid = self.generate_test_hwid()
            
            # Build API URL for license validation
            license_url = (f"{self.API_URL}?type=license&key={license_key}"
                          f"&sessionid={self.session_id}&name={self.APP_NAME}"
                          f"&ownerid={self.OWNER_ID}&hwid={hwid}")
            
            self.log(f"License URL: {license_url}")
            
            # Make GET request
            headers = {"User-Agent": "KeyAuth"}
            response = requests.get(license_url, headers=headers, timeout=10)
            
            self.log(f"Response status: {response.status_code}")
            self.log(f"Response body: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    success = data.get("success", False)
                    message = data.get("message", "Unknown response")
                    user_info = data.get("info", {})
                    
                    if success:
                        self.log("License validation successful!")
                        return True, message, user_info
                    else:
                        self.log(f"License validation failed: {message}", "ERROR")
                        return False, message, {}
                        
                except json.JSONDecodeError as e:
                    self.log(f"Failed to parse JSON response: {e}", "ERROR")
                    return False, f"Invalid JSON response: {response.text}", {}
            else:
                self.log(f"HTTP error: {response.status_code}", "ERROR")
                return False, f"HTTP error: {response.status_code}", {}
                
        except requests.RequestException as e:
            self.log(f"Network error: {e}", "ERROR")
            return False, f"Network error: {e}", {}
    
    def test_configuration(self) -> bool:
        """Test KeyAuth configuration"""
        self.log("Testing KeyAuth configuration...")
        
        print("\n=== KeyAuth Configuration Test ===")
        print(f"API URL: {self.API_URL}")
        print(f"App Name: {self.APP_NAME}")
        print(f"Owner ID: {self.OWNER_ID}")
        print(f"Version: {self.VERSION}")
        print(f"App Hash: {self.APP_HASH}")
        
        # Test session initialization
        success, message = self.initialize_session()
        
        print(f"\nSession Initialization: {'✅ PASS' if success else '❌ FAIL'}")
        print(f"Message: {message}")
        
        if success:
            print(f"Session ID: {self.session_id}")
            
        return success
    
    def full_test(self, license_key: str) -> bool:
        """Perform full KeyAuth test with license validation"""
        print("\n=== Full KeyAuth API Test ===")
        
        # Step 1: Initialize session
        print("\n1. Initializing session...")
        success, message = self.initialize_session()
        
        if not success:
            print(f"❌ Session initialization failed: {message}")
            return False
        
        print(f"✅ Session initialized: {self.session_id}")
        
        # Step 2: Validate license
        print(f"\n2. Validating license key: {license_key[:8]}...")
        success, message, user_info = self.validate_license(license_key)
        
        if not success:
            print(f"❌ License validation failed: {message}")
            return False
        
        print(f"✅ License validation successful: {message}")
        
        # Step 3: Display user information
        if user_info:
            print(f"\n3. User Information:")
            for key, value in user_info.items():
                print(f"   {key}: {value}")
        
        return True


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="KeyAuth API Test Utility for BearMod",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python keyauth_test_utility.py --license YOUR_LICENSE_KEY
  python keyauth_test_utility.py --license YOUR_LICENSE_KEY --verbose
  python keyauth_test_utility.py --test-config
        """
    )
    
    parser.add_argument(
        "--license", "-l",
        help="License key to validate"
    )
    
    parser.add_argument(
        "--test-config", "-t",
        action="store_true",
        help="Test KeyAuth configuration only (no license validation)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    if not args.license and not args.test_config:
        parser.print_help()
        sys.exit(1)
    
    # Create tester instance
    tester = KeyAuthTester(verbose=args.verbose)
    
    try:
        if args.test_config:
            # Test configuration only
            success = tester.test_configuration()
            sys.exit(0 if success else 1)
        
        elif args.license:
            # Full test with license validation
            success = tester.full_test(args.license)
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
