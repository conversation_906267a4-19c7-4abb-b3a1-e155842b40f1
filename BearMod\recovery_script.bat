@echo off
setlocal enabledelayedexpansion
echo ========================================
echo BearMod Installation Recovery Script v2.0
echo ========================================
echo Target: com.bearmod.loader v3.8.0 (versionCode 100)
echo.

REM Check if ADB is available
echo [INFO] Checking ADB availability...
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] ADB is not available or not in PATH
    echo Please install Android SDK Platform Tools and add to PATH
    echo Download from: https://developer.android.com/studio/releases/platform-tools
    pause
    exit /b 1
)
echo [OK] ADB is available

echo.
echo [STEP 1] Checking connected devices...
adb devices
for /f "tokens=1" %%i in ('adb devices ^| find /c "device"') do set device_count=%%i
if %device_count% lss 2 (
    echo [WARNING] No devices detected. Please:
    echo 1. Connect your Android device via USB
    echo 2. Enable USB Debugging in Developer Options
    echo 3. Accept the USB debugging prompt on your device
    pause
)

echo.
echo [STEP 2] Completely removing existing BearMod installation...
echo [INFO] Uninstalling com.bearmod.loader...
adb uninstall com.bearmod.loader >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Previous installation removed
) else (
    echo [INFO] No previous installation found (this is normal)
)

echo [INFO] Clearing package manager data...
adb shell pm clear com.bearmod.loader >nul 2>&1
echo [INFO] Removing data directories...
adb shell rm -rf /data/data/com.bearmod.loader >nul 2>&1
adb shell rm -rf /sdcard/Android/data/com.bearmod.loader >nul 2>&1

echo.
echo [STEP 3] Clearing system cache...
echo [INFO] Clearing package installer cache...
adb shell pm clear com.android.packageinstaller >nul 2>&1
adb shell pm clear com.google.android.packageinstaller >nul 2>&1
echo [OK] System cache cleared

echo.
echo [STEP 4] Installing new BearMod APK...
set APK_PATH=app\build\outputs\apk\debug\app-debug.apk
if exist "%APK_PATH%" (
    echo [INFO] Installing %APK_PATH%...
    echo [INFO] APK Version: 3.8.0 (versionCode 100)
    adb install -r "%APK_PATH%"
    if %errorlevel% equ 0 (
        echo [SUCCESS] BearMod installed successfully!
        set INSTALL_SUCCESS=1
    ) else (
        echo [ERROR] Installation failed with exit code %errorlevel%
        echo Possible causes:
        echo - Device not connected or USB debugging disabled
        echo - Insufficient storage space
        echo - Package signature conflicts
        set INSTALL_SUCCESS=0
    )
) else (
    echo [ERROR] APK file not found at: %APK_PATH%
    echo Please build the project first: gradlew assembleDebug
    set INSTALL_SUCCESS=0
)

echo.
echo [STEP 5] Verification...
echo [INFO] Checking if BearMod is installed...
adb shell pm list packages | findstr com.bearmod.loader >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] BearMod package is installed

    REM Get installed version info
    for /f "tokens=*" %%i in ('adb shell dumpsys package com.bearmod.loader ^| findstr "versionCode"') do (
        echo [INFO] %%i
    )

    echo [INFO] Getting app info...
    adb shell pm dump com.bearmod.loader | findstr "versionName\|versionCode\|targetSdk" 2>nul

) else (
    echo [WARNING] BearMod package not found in installed packages
    set INSTALL_SUCCESS=0
)

echo.
echo ========================================
if %INSTALL_SUCCESS%==1 (
    echo [SUCCESS] Recovery completed successfully!
    echo ========================================
    echo.
    echo [NEXT STEPS]
    echo 1. Launch BearMod app on your device
    echo 2. Test authentication with your existing license key
    echo 3. Monitor authentication logs:
    echo    adb logcat -s BearModAuth:* AuthRecovery:* NativeSecurityManager:*
    echo.
    echo [TROUBLESHOOTING]
    echo If authentication fails, run:
    echo    adb logcat -c ^&^& adb logcat -s BearModAuth
    echo.
) else (
    echo [FAILED] Recovery encountered errors!
    echo ========================================
    echo.
    echo [TROUBLESHOOTING STEPS]
    echo 1. Ensure device is connected: adb devices
    echo 2. Check USB debugging is enabled
    echo 3. Try manual installation:
    echo    adb install -r -d "%APK_PATH%"
    echo 4. Check device storage space
    echo.
)
pause
