@echo off
echo ========================================
echo BearMod Installation Recovery Script
echo ========================================
echo.

REM Check if ADB is available
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: ADB is not available or not in PATH
    echo Please install Android SDK Platform Tools and add to PATH
    pause
    exit /b 1
)

echo Step 1: Checking connected devices...
adb devices
echo.

echo Step 2: Completely removing existing BearMod installation...
echo Uninstalling com.bearmod.loader...
adb uninstall com.bearmod.loader 2>nul
echo Clearing package manager data...
adb shell pm clear com.bearmod.loader 2>nul
echo Removing data directories...
adb shell rm -rf /data/data/com.bearmod.loader 2>nul
adb shell rm -rf /sdcard/Android/data/com.bearmod.loader 2>nul
echo.

echo Step 3: Clearing system cache...
echo Clearing package installer cache...
adb shell pm clear com.android.packageinstaller 2>nul
adb shell pm clear com.google.android.packageinstaller 2>nul
echo.

echo Step 4: Installing new BearMod APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo Installing app-debug.apk...
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
    if %errorlevel% equ 0 (
        echo SUCCESS: BearMod installed successfully!
    ) else (
        echo ERROR: Installation failed. Check device connection and USB debugging.
    )
) else (
    echo ERROR: APK file not found. Please build the project first.
    echo Run: gradlew assembleDebug
)
echo.

echo Step 5: Verification...
echo Checking if BearMod is installed...
adb shell pm list packages | findstr com.bearmod.loader
if %errorlevel% equ 0 (
    echo SUCCESS: BearMod package is installed
) else (
    echo WARNING: BearMod package not found
)
echo.

echo ========================================
echo Recovery script completed!
echo ========================================
echo.
echo Next steps:
echo 1. Launch BearMod app on your device
echo 2. Test authentication with your license key
echo 3. Check logs if authentication fails:
echo    adb logcat -s BearModAuth
echo.
pause
