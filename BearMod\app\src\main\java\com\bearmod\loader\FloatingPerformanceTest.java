package com.bearmod;

import android.content.Context;
import android.util.Log;
import android.os.Debug;
import java.lang.ref.WeakReference;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Performance testing suite for optimized Floating service
 * Tests memory management, thread safety, and resource cleanup
 */
public class FloatingPerformanceTest {
    private static final String TAG = "FloatingPerformanceTest";
    
    /**
     * Run comprehensive performance tests
     */
    public static void runAllTests(Context context) {
        Log.i(TAG, "=== Starting Floating Service Performance Tests ===");
        
        try {
            testMemoryUsage();
            testThreadSafety();
            testResourceCleanup();
            testServiceLifecycle(context);
            testUIResponsiveness();
            
            Log.i(TAG, "=== All Performance Tests Completed Successfully ===");
            
        } catch (Exception e) {
            Log.e(TAG, "Performance test failed", e);
        }
    }
    
    /**
     * Test memory usage and leak detection
     */
    public static void testMemoryUsage() {
        Log.i(TAG, "=== Memory Usage Test ===");
        
        try {
            // Get initial memory usage
            long initialMemory = getUsedMemory();
            Log.i(TAG, "Initial memory usage: " + (initialMemory / 1024) + " KB");
            
            // Simulate service operations
            Floating service = Floating.getInstance();
            if (service != null) {
                // Test configuration operations
                for (int i = 0; i < 100; i++) {
                    service.UpdateConfiguration2("TEST_KEY_" + i, i);
                }
                
                // Force garbage collection
                System.gc();
                Thread.sleep(100);
                
                long afterOperationsMemory = getUsedMemory();
                Log.i(TAG, "Memory after operations: " + (afterOperationsMemory / 1024) + " KB");
                
                // Calculate memory increase
                long memoryIncrease = afterOperationsMemory - initialMemory;
                Log.i(TAG, "Memory increase: " + (memoryIncrease / 1024) + " KB");
                
                // Test should pass if memory increase is reasonable (< 1MB)
                boolean memoryTestPassed = memoryIncrease < 1024 * 1024;
                Log.i(TAG, "Memory test: " + (memoryTestPassed ? "PASSED" : "FAILED"));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Memory test failed", e);
        }
    }
    
    /**
     * Test thread safety of concurrent operations
     */
    public static void testThreadSafety() {
        Log.i(TAG, "=== Thread Safety Test ===");
        
        try {
            Floating service = Floating.getInstance();
            if (service == null) {
                Log.w(TAG, "Service not available for thread safety test");
                return;
            }
            
            final int threadCount = 10;
            final int operationsPerThread = 50;
            final CountDownLatch latch = new CountDownLatch(threadCount);
            final boolean[] results = new boolean[threadCount];
            
            // Create multiple threads performing concurrent operations
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                new Thread(() -> {
                    try {
                        for (int j = 0; j < operationsPerThread; j++) {
                            // Test concurrent configuration updates
                            service.UpdateConfiguration2("THREAD_" + threadId + "_KEY_" + j, j);
                            
                            // Test concurrent language operations
                            LanguageManager.getInstance().get("Test Key " + j);
                            
                            // Small delay to increase chance of race conditions
                            Thread.sleep(1);
                        }
                        results[threadId] = true;
                        
                    } catch (Exception e) {
                        Log.e(TAG, "Thread " + threadId + " failed", e);
                        results[threadId] = false;
                    } finally {
                        latch.countDown();
                    }
                }).start();
            }
            
            // Wait for all threads to complete
            boolean completed = latch.await(10, TimeUnit.SECONDS);
            
            if (completed) {
                boolean allPassed = true;
                for (boolean result : results) {
                    if (!result) {
                        allPassed = false;
                        break;
                    }
                }
                Log.i(TAG, "Thread safety test: " + (allPassed ? "PASSED" : "FAILED"));
            } else {
                Log.e(TAG, "Thread safety test: TIMEOUT");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Thread safety test failed", e);
        }
    }
    
    /**
     * Test resource cleanup and memory leak prevention
     */
    public static void testResourceCleanup() {
        Log.i(TAG, "=== Resource Cleanup Test ===");
        
        try {
            // Test WeakReference cleanup
            WeakReference<Object> testRef = new WeakReference<>(new Object());
            System.gc();
            Thread.sleep(100);
            
            boolean weakRefCleared = testRef.get() == null;
            Log.i(TAG, "WeakReference cleanup: " + (weakRefCleared ? "PASSED" : "FAILED"));
            
            // Test LanguageManager listener cleanup
            LanguageManager langManager = LanguageManager.getInstance();
            langManager.addLanguageChangeListener("test_cleanup", newLanguage -> {
                // Test listener
            });
            
            langManager.removeLanguageChangeListener("test_cleanup");
            Log.i(TAG, "LanguageManager listener cleanup: PASSED");
            
            // Test configuration map cleanup
            Floating service = Floating.getInstance();
            if (service != null) {
                int initialConfigSize = service.getConfigMapSize();
                
                // Add test configurations
                for (int i = 0; i < 10; i++) {
                    service.UpdateConfiguration2("CLEANUP_TEST_" + i, i);
                }
                
                // Clear test configurations
                service.clearTestConfigurations();
                
                int finalConfigSize = service.getConfigMapSize();
                boolean configCleanup = finalConfigSize <= initialConfigSize + 1; // Allow some tolerance
                
                Log.i(TAG, "Configuration cleanup: " + (configCleanup ? "PASSED" : "FAILED"));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Resource cleanup test failed", e);
        }
    }
    
    /**
     * Test service lifecycle management
     */
    public static void testServiceLifecycle(Context context) {
        Log.i(TAG, "=== Service Lifecycle Test ===");
        
        try {
            Floating service = Floating.getInstance();
            if (service == null) {
                Log.w(TAG, "Service not available for lifecycle test");
                return;
            }
            
            // Test service state
            boolean isRunning = service.isServiceRunning();
            Log.i(TAG, "Service running state: " + isRunning);
            
            // Test initialization state
            boolean isInitialized = service.isInitialized();
            Log.i(TAG, "Service initialized state: " + isInitialized);
            
            // Test configuration persistence
            service.UpdateConfiguration2("LIFECYCLE_TEST", "test_value");
            service.SaveConfiguration();
            
            String retrievedValue = service.GetString("LIFECYCLE_TEST", "default");
            boolean configPersistence = "test_value".equals(retrievedValue);
            
            Log.i(TAG, "Configuration persistence: " + (configPersistence ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Service lifecycle test failed", e);
        }
    }
    
    /**
     * Test UI responsiveness and performance
     */
    public static void testUIResponsiveness() {
        Log.i(TAG, "=== UI Responsiveness Test ===");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // Test language switching performance
            LanguageManager langManager = LanguageManager.getInstance();
            for (int i = 0; i < 10; i++) {
                langManager.setLanguage("en");
                langManager.setLanguage("zh");
                langManager.setLanguage("ru");
            }
            
            long languageSwitchTime = System.currentTimeMillis() - startTime;
            Log.i(TAG, "Language switching time (30 switches): " + languageSwitchTime + "ms");
            
            // Test translation retrieval performance
            startTime = System.currentTimeMillis();
            for (int i = 0; i < 1000; i++) {
                langManager.get("Main Menu");
                langManager.get("Weapon");
                langManager.get("Aimbot");
            }
            
            long translationTime = System.currentTimeMillis() - startTime;
            Log.i(TAG, "Translation retrieval time (3000 calls): " + translationTime + "ms");
            
            // Performance thresholds
            boolean languagePerformance = languageSwitchTime < 1000; // < 1 second for 30 switches
            boolean translationPerformance = translationTime < 100; // < 100ms for 3000 calls
            
            Log.i(TAG, "Language switching performance: " + (languagePerformance ? "PASSED" : "FAILED"));
            Log.i(TAG, "Translation performance: " + (translationPerformance ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "UI responsiveness test failed", e);
        }
    }
    
    /**
     * Get current memory usage
     */
    private static long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * Get native heap size for memory analysis
     */
    private static long getNativeHeapSize() {
        return Debug.getNativeHeapSize();
    }
    
    /**
     * Log memory statistics
     */
    public static void logMemoryStats() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        Log.i(TAG, "=== Memory Statistics ===");
        Log.i(TAG, "Max memory: " + (maxMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Total memory: " + (totalMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Used memory: " + (usedMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Free memory: " + (freeMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Native heap: " + (getNativeHeapSize() / 1024 / 1024) + " MB");
    }
}
