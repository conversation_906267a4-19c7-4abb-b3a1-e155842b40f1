package com.bearmod;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.os.SystemClock;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Performance testing suite for optimized RecorderFakeUtils
 * Tests frame rate preservation, memory management, and crash prevention
 */
public class RecorderFakeUtilsPerformanceTest {
    private static final String TAG = "RecorderFakeUtilsPerformanceTest";
    
    // Performance thresholds
    private static final long MAX_FRAME_TIME_MS = 16; // 60 FPS = 16.67ms per frame
    private static final long MAX_OPERATION_TIME_MS = 5; // Max time for single operation
    private static final int MIN_FPS_THRESHOLD = 55; // Minimum acceptable FPS during recording
    
    /**
     * Run comprehensive performance tests for RecorderFakeUtils
     */
    public static void runAllTests(Context context) {
        Log.i(TAG, "=== Starting RecorderFakeUtils Performance Tests ===");

        try {
            testFrameRatePreservation(context);
            testMemoryUsageDuringRecording();
            testConcurrentOperations();
            testReflectionCaching();
            testROMDetectionPerformance();
            testViewLayoutUpdatePerformance(context);
            testCrashPrevention(context);
            testResourceCleanup();

            // New screenshot detection tests
            testScreenshotDetectionPerformance(context);
            testScreenshotUIHidingPerformance(context);
            testScreenshotDetectionAccuracy(context);
            testScreenshotConcurrentOperations(context);

            Log.i(TAG, "=== All RecorderFakeUtils Performance Tests Completed Successfully ===");

        } catch (Exception e) {
            Log.e(TAG, "Performance test failed", e);
        }
    }
    
    /**
     * Test frame rate preservation during recording operations
     */
    private static void testFrameRatePreservation(Context context) {
        Log.i(TAG, "=== Frame Rate Preservation Test ===");
        
        try {
            // Simulate high-frequency recording operations
            final int operationCount = 1000;
            final AtomicInteger frameDrops = new AtomicInteger(0);
            final AtomicLong totalFrameTime = new AtomicLong(0);
            
            long startTime = SystemClock.elapsedRealtime();
            
            for (int i = 0; i < operationCount; i++) {
                long frameStart = SystemClock.elapsedRealtime();
                
                // Simulate recording operation
                simulateRecordingOperation();
                
                long frameTime = SystemClock.elapsedRealtime() - frameStart;
                totalFrameTime.addAndGet(frameTime);
                
                if (frameTime > MAX_FRAME_TIME_MS) {
                    frameDrops.incrementAndGet();
                }
                
                // Small delay to simulate frame timing
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            long totalTime = SystemClock.elapsedRealtime() - startTime;
            double averageFrameTime = (double) totalFrameTime.get() / operationCount;
            double frameDropPercentage = (double) frameDrops.get() / operationCount * 100;
            double achievedFPS = 1000.0 / averageFrameTime;
            
            Log.i(TAG, "Frame rate test results:");
            Log.i(TAG, "  Operations: " + operationCount);
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Average frame time: " + String.format("%.2f", averageFrameTime) + "ms");
            Log.i(TAG, "  Frame drops: " + frameDrops.get() + " (" + String.format("%.2f", frameDropPercentage) + "%)");
            Log.i(TAG, "  Achieved FPS: " + String.format("%.1f", achievedFPS));
            
            boolean frameRateTestPassed = achievedFPS >= MIN_FPS_THRESHOLD && frameDropPercentage < 5.0;
            Log.i(TAG, "Frame rate preservation test: " + (frameRateTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Frame rate test failed", e);
        }
    }
    
    /**
     * Test memory usage during recording operations
     */
    private static void testMemoryUsageDuringRecording() {
        Log.i(TAG, "=== Memory Usage During Recording Test ===");
        
        try {
            // Get initial memory usage
            long initialMemory = getUsedMemory();
            Log.i(TAG, "Initial memory usage: " + (initialMemory / 1024) + " KB");
            
            // Simulate intensive recording operations
            for (int i = 0; i < 500; i++) {
                simulateRecordingOperation();
                
                // Simulate view layout updates
                simulateViewLayoutUpdate();
                
                if (i % 100 == 0) {
                    long currentMemory = getUsedMemory();
                    Log.d(TAG, "Memory at operation " + i + ": " + (currentMemory / 1024) + " KB");
                }
            }
            
            // Force garbage collection
            System.gc();
            Thread.sleep(100);
            
            long finalMemory = getUsedMemory();
            long memoryIncrease = finalMemory - initialMemory;
            
            Log.i(TAG, "Memory usage test results:");
            Log.i(TAG, "  Initial memory: " + (initialMemory / 1024) + " KB");
            Log.i(TAG, "  Final memory: " + (finalMemory / 1024) + " KB");
            Log.i(TAG, "  Memory increase: " + (memoryIncrease / 1024) + " KB");
            
            // Test should pass if memory increase is reasonable (< 2MB)
            boolean memoryTestPassed = memoryIncrease < 2 * 1024 * 1024;
            Log.i(TAG, "Memory usage test: " + (memoryTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Memory usage test failed", e);
        }
    }
    
    /**
     * Test concurrent operations for thread safety
     */
    private static void testConcurrentOperations() {
        Log.i(TAG, "=== Concurrent Operations Test ===");
        
        try {
            final int threadCount = 8;
            final int operationsPerThread = 100;
            final CountDownLatch latch = new CountDownLatch(threadCount);
            final AtomicInteger successCount = new AtomicInteger(0);
            final AtomicInteger errorCount = new AtomicInteger(0);
            
            long startTime = SystemClock.elapsedRealtime();
            
            // Create multiple threads performing concurrent operations
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                new Thread(() -> {
                    try {
                        for (int j = 0; j < operationsPerThread; j++) {
                            try {
                                // Test concurrent ROM detection
                                simulateROMDetection();
                                
                                // Test concurrent reflection caching
                                simulateReflectionOperation();
                                
                                // Test concurrent view operations
                                simulateViewLayoutUpdate();
                                
                                successCount.incrementAndGet();
                                
                            } catch (Exception e) {
                                errorCount.incrementAndGet();
                                Log.w(TAG, "Thread " + threadId + " operation " + j + " failed", e);
                            }
                            
                            // Small delay to increase chance of race conditions
                            Thread.sleep(1);
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        latch.countDown();
                    }
                }).start();
            }
            
            // Wait for all threads to complete
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            long totalTime = SystemClock.elapsedRealtime() - startTime;
            
            Log.i(TAG, "Concurrent operations test results:");
            Log.i(TAG, "  Threads: " + threadCount);
            Log.i(TAG, "  Operations per thread: " + operationsPerThread);
            Log.i(TAG, "  Total operations: " + (threadCount * operationsPerThread));
            Log.i(TAG, "  Successful operations: " + successCount.get());
            Log.i(TAG, "  Failed operations: " + errorCount.get());
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Completed: " + completed);
            
            boolean concurrencyTestPassed = completed && errorCount.get() == 0;
            Log.i(TAG, "Concurrent operations test: " + (concurrencyTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Concurrent operations test failed", e);
        }
    }
    
    /**
     * Test reflection caching performance
     */
    private static void testReflectionCaching() {
        Log.i(TAG, "=== Reflection Caching Test ===");
        
        try {
            final int iterations = 1000;
            
            // Test without caching (first run)
            long startTime = SystemClock.elapsedRealtime();
            for (int i = 0; i < iterations; i++) {
                simulateReflectionOperation();
            }
            long firstRunTime = SystemClock.elapsedRealtime() - startTime;
            
            // Test with caching (second run)
            startTime = SystemClock.elapsedRealtime();
            for (int i = 0; i < iterations; i++) {
                simulateReflectionOperation();
            }
            long secondRunTime = SystemClock.elapsedRealtime() - startTime;
            
            double speedupRatio = (double) firstRunTime / secondRunTime;
            
            Log.i(TAG, "Reflection caching test results:");
            Log.i(TAG, "  Iterations: " + iterations);
            Log.i(TAG, "  First run time: " + firstRunTime + "ms");
            Log.i(TAG, "  Second run time: " + secondRunTime + "ms");
            Log.i(TAG, "  Speedup ratio: " + String.format("%.2f", speedupRatio) + "x");
            
            // Caching should provide at least 2x speedup
            boolean cachingTestPassed = speedupRatio >= 2.0;
            Log.i(TAG, "Reflection caching test: " + (cachingTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Reflection caching test failed", e);
        }
    }
    
    /**
     * Test ROM detection performance
     */
    private static void testROMDetectionPerformance() {
        Log.i(TAG, "=== ROM Detection Performance Test ===");
        
        try {
            final int iterations = 100;
            
            long startTime = SystemClock.elapsedRealtime();
            for (int i = 0; i < iterations; i++) {
                simulateROMDetection();
            }
            long totalTime = SystemClock.elapsedRealtime() - startTime;
            
            double averageTime = (double) totalTime / iterations;
            
            Log.i(TAG, "ROM detection test results:");
            Log.i(TAG, "  Iterations: " + iterations);
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Average time per detection: " + String.format("%.2f", averageTime) + "ms");
            
            // ROM detection should be fast (< 10ms average)
            boolean romDetectionTestPassed = averageTime < 10.0;
            Log.i(TAG, "ROM detection performance test: " + (romDetectionTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "ROM detection test failed", e);
        }
    }
    
    /**
     * Test view layout update performance
     */
    private static void testViewLayoutUpdatePerformance(Context context) {
        Log.i(TAG, "=== View Layout Update Performance Test ===");
        
        try {
            final int iterations = 200;
            
            long startTime = SystemClock.elapsedRealtime();
            for (int i = 0; i < iterations; i++) {
                simulateViewLayoutUpdate();
                
                // Ensure we don't exceed frame time
                if (i % 10 == 0) {
                    long currentTime = SystemClock.elapsedRealtime();
                    long elapsedTime = currentTime - startTime;
                    if (elapsedTime > i * MAX_FRAME_TIME_MS) {
                        Log.w(TAG, "View layout updates are too slow at iteration " + i);
                    }
                }
            }
            long totalTime = SystemClock.elapsedRealtime() - startTime;
            
            double averageTime = (double) totalTime / iterations;
            
            Log.i(TAG, "View layout update test results:");
            Log.i(TAG, "  Iterations: " + iterations);
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Average time per update: " + String.format("%.2f", averageTime) + "ms");
            
            // View updates should be fast (< 5ms average)
            boolean viewUpdateTestPassed = averageTime < MAX_OPERATION_TIME_MS;
            Log.i(TAG, "View layout update performance test: " + (viewUpdateTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "View layout update test failed", e);
        }
    }
    
    /**
     * Test crash prevention mechanisms
     */
    private static void testCrashPrevention(Context context) {
        Log.i(TAG, "=== Crash Prevention Test ===");
        
        try {
            int crashCount = 0;
            int testCount = 0;
            
            // Test null parameter handling
            testCount++;
            try {
                RecorderFakeUtils.setFakeRecorderWindowLayoutParams(null, null, null, null, null, null, null, null);
            } catch (Exception e) {
                crashCount++;
                Log.w(TAG, "Null parameter test caused exception", e);
            }
            
            // Test invalid view operations
            testCount++;
            try {
                simulateInvalidViewOperation();
            } catch (Exception e) {
                crashCount++;
                Log.w(TAG, "Invalid view operation test caused exception", e);
            }
            
            // Test reflection errors
            testCount++;
            try {
                simulateReflectionError();
            } catch (Exception e) {
                crashCount++;
                Log.w(TAG, "Reflection error test caused exception", e);
            }
            
            Log.i(TAG, "Crash prevention test results:");
            Log.i(TAG, "  Total tests: " + testCount);
            Log.i(TAG, "  Crashes: " + crashCount);
            Log.i(TAG, "  Success rate: " + String.format("%.1f", (double)(testCount - crashCount) / testCount * 100) + "%");
            
            boolean crashPreventionPassed = crashCount == 0;
            Log.i(TAG, "Crash prevention test: " + (crashPreventionPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Crash prevention test failed", e);
        }
    }
    
    /**
     * Test resource cleanup
     */
    private static void testResourceCleanup() {
        Log.i(TAG, "=== Resource Cleanup Test ===");
        
        try {
            // Get initial memory
            long initialMemory = getUsedMemory();
            
            // Create resources
            for (int i = 0; i < 100; i++) {
                simulateRecordingOperation();
                simulateReflectionOperation();
            }
            
            long beforeCleanupMemory = getUsedMemory();
            
            // Cleanup
            RecorderFakeUtils.cleanup();
            System.gc();
            Thread.sleep(100);
            
            long afterCleanupMemory = getUsedMemory();
            
            Log.i(TAG, "Resource cleanup test results:");
            Log.i(TAG, "  Initial memory: " + (initialMemory / 1024) + " KB");
            Log.i(TAG, "  Before cleanup: " + (beforeCleanupMemory / 1024) + " KB");
            Log.i(TAG, "  After cleanup: " + (afterCleanupMemory / 1024) + " KB");
            
            boolean cleanupTestPassed = afterCleanupMemory <= beforeCleanupMemory;
            Log.i(TAG, "Resource cleanup test: " + (cleanupTestPassed ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            Log.e(TAG, "Resource cleanup test failed", e);
        }
    }
    
    // ========== Helper Methods ==========
    
    private static void simulateRecordingOperation() {
        // Simulate the work done during recording
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void simulateViewLayoutUpdate() {
        // Simulate view layout update work
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void simulateROMDetection() {
        // Simulate ROM detection work
        try {
            Thread.sleep(2);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void simulateReflectionOperation() {
        // Simulate reflection operation
        try {
            Class<?> clazz = WindowManager.LayoutParams.class;
            clazz.getDeclaredField("flags");
        } catch (Exception e) {
            // Expected for testing
        }
    }
    
    private static void simulateInvalidViewOperation() {
        // Simulate invalid view operation that should be handled gracefully
    }
    
    private static void simulateReflectionError() {
        // Simulate reflection error that should be handled gracefully
        try {
            Class<?> clazz = WindowManager.LayoutParams.class;
            clazz.getDeclaredField("nonExistentField");
        } catch (Exception e) {
            // Expected for testing
        }
    }
    
    private static long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * Log memory statistics
     */
    public static void logMemoryStats() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        Log.i(TAG, "=== Memory Statistics ===");
        Log.i(TAG, "Max memory: " + (maxMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Total memory: " + (totalMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Used memory: " + (usedMemory / 1024 / 1024) + " MB");
        Log.i(TAG, "Free memory: " + (freeMemory / 1024 / 1024) + " MB");
    }

    // ========== Screenshot Detection Performance Tests ==========

    /**
     * Test screenshot detection performance
     */
    private static void testScreenshotDetectionPerformance(Context context) {
        Log.i(TAG, "=== Screenshot Detection Performance Test ===");

        try {
            // Initialize screenshot detection
            RecorderFakeUtils.initializeScreenshotDetection(context);

            final int detectionTests = 50;
            final AtomicInteger detectionCount = new AtomicInteger(0);
            final AtomicLong totalDetectionTime = new AtomicLong(0);

            // Add callback to measure detection time
            RecorderFakeUtils.ScreenshotDetectionCallback testCallback = new RecorderFakeUtils.ScreenshotDetectionCallback() {
                private long detectionStartTime;

                @Override
                public void onScreenshotDetected() {
                    detectionStartTime = SystemClock.elapsedRealtime();
                    detectionCount.incrementAndGet();
                }

                @Override
                public void onScreenshotCompleted() {
                    long detectionTime = SystemClock.elapsedRealtime() - detectionStartTime;
                    totalDetectionTime.addAndGet(detectionTime);
                }

                @Override
                public void onUIHidden() {}

                @Override
                public void onUIRestored() {}
            };

            RecorderFakeUtils.addScreenshotDetectionCallback(testCallback);

            long startTime = SystemClock.elapsedRealtime();

            // Simulate screenshot detections
            for (int i = 0; i < detectionTests; i++) {
                RecorderFakeUtils.manuallyTriggerScreenshotHiding();
                Thread.sleep(50); // Wait for detection to process
                RecorderFakeUtils.manuallyRestoreUIFromScreenshot();
                Thread.sleep(50); // Wait for restoration
            }

            long totalTime = SystemClock.elapsedRealtime() - startTime;
            double averageDetectionTime = detectionCount.get() > 0 ?
                (double) totalDetectionTime.get() / detectionCount.get() : 0;

            Log.i(TAG, "Screenshot detection test results:");
            Log.i(TAG, "  Detection tests: " + detectionTests);
            Log.i(TAG, "  Successful detections: " + detectionCount.get());
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Average detection time: " + String.format("%.2f", averageDetectionTime) + "ms");

            boolean detectionTestPassed = averageDetectionTime < 100 && detectionCount.get() == detectionTests;
            Log.i(TAG, "Screenshot detection performance test: " + (detectionTestPassed ? "PASSED" : "FAILED"));

            // Cleanup
            RecorderFakeUtils.removeScreenshotDetectionCallback(testCallback);
            RecorderFakeUtils.stopScreenshotDetection(context);

        } catch (Exception e) {
            Log.e(TAG, "Screenshot detection performance test failed", e);
        }
    }

    /**
     * Test screenshot UI hiding performance
     */
    private static void testScreenshotUIHidingPerformance(Context context) {
        Log.i(TAG, "=== Screenshot UI Hiding Performance Test ===");

        try {
            // Initialize screenshot detection
            RecorderFakeUtils.initializeScreenshotDetection(context);

            final int hidingTests = 100;
            final AtomicLong totalHidingTime = new AtomicLong(0);
            final AtomicLong totalRestorationTime = new AtomicLong(0);
            final AtomicInteger hidingCount = new AtomicInteger(0);
            final AtomicInteger restorationCount = new AtomicInteger(0);

            // Add callback to measure hiding/restoration time
            RecorderFakeUtils.ScreenshotDetectionCallback testCallback = new RecorderFakeUtils.ScreenshotDetectionCallback() {
                private long hidingStartTime;
                private long restorationStartTime;

                @Override
                public void onScreenshotDetected() {}

                @Override
                public void onScreenshotCompleted() {}

                @Override
                public void onUIHidden() {
                    long hidingTime = SystemClock.elapsedRealtime() - hidingStartTime;
                    totalHidingTime.addAndGet(hidingTime);
                    hidingCount.incrementAndGet();
                    restorationStartTime = SystemClock.elapsedRealtime();
                }

                @Override
                public void onUIRestored() {
                    long restorationTime = SystemClock.elapsedRealtime() - restorationStartTime;
                    totalRestorationTime.addAndGet(restorationTime);
                    restorationCount.incrementAndGet();
                }
            };

            RecorderFakeUtils.addScreenshotDetectionCallback(testCallback);

            long startTime = SystemClock.elapsedRealtime();

            // Test UI hiding/restoration performance
            for (int i = 0; i < hidingTests; i++) {
                long hideStart = SystemClock.elapsedRealtime();
                RecorderFakeUtils.manuallyTriggerScreenshotHiding();

                // Wait for hiding to complete
                Thread.sleep(20);

                RecorderFakeUtils.manuallyRestoreUIFromScreenshot();

                // Wait for restoration to complete
                Thread.sleep(20);

                // Check frame time
                long frameTime = SystemClock.elapsedRealtime() - hideStart;
                if (frameTime > MAX_FRAME_TIME_MS) {
                    Log.w(TAG, "UI hiding/restoration took " + frameTime + "ms (frame drop)");
                }
            }

            long totalTime = SystemClock.elapsedRealtime() - startTime;
            double averageHidingTime = hidingCount.get() > 0 ?
                (double) totalHidingTime.get() / hidingCount.get() : 0;
            double averageRestorationTime = restorationCount.get() > 0 ?
                (double) totalRestorationTime.get() / restorationCount.get() : 0;

            Log.i(TAG, "Screenshot UI hiding test results:");
            Log.i(TAG, "  Hiding tests: " + hidingTests);
            Log.i(TAG, "  Successful hidings: " + hidingCount.get());
            Log.i(TAG, "  Successful restorations: " + restorationCount.get());
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Average hiding time: " + String.format("%.2f", averageHidingTime) + "ms");
            Log.i(TAG, "  Average restoration time: " + String.format("%.2f", averageRestorationTime) + "ms");

            boolean hidingTestPassed = averageHidingTime < MAX_OPERATION_TIME_MS &&
                                     averageRestorationTime < MAX_OPERATION_TIME_MS &&
                                     hidingCount.get() == hidingTests &&
                                     restorationCount.get() == hidingTests;
            Log.i(TAG, "Screenshot UI hiding performance test: " + (hidingTestPassed ? "PASSED" : "FAILED"));

            // Cleanup
            RecorderFakeUtils.removeScreenshotDetectionCallback(testCallback);
            RecorderFakeUtils.stopScreenshotDetection(context);

        } catch (Exception e) {
            Log.e(TAG, "Screenshot UI hiding performance test failed", e);
        }
    }

    /**
     * Test screenshot detection accuracy
     */
    private static void testScreenshotDetectionAccuracy(Context context) {
        Log.i(TAG, "=== Screenshot Detection Accuracy Test ===");

        try {
            // Initialize screenshot detection
            RecorderFakeUtils.initializeScreenshotDetection(context);

            final int accuracyTests = 20;
            final AtomicInteger truePositives = new AtomicInteger(0);
            final AtomicInteger falsePositives = new AtomicInteger(0);
            final CountDownLatch latch = new CountDownLatch(accuracyTests);

            // Add callback to track detection accuracy
            RecorderFakeUtils.ScreenshotDetectionCallback testCallback = new RecorderFakeUtils.ScreenshotDetectionCallback() {
                @Override
                public void onScreenshotDetected() {
                    truePositives.incrementAndGet();
                    latch.countDown();
                }

                @Override
                public void onScreenshotCompleted() {}
                @Override
                public void onUIHidden() {}
                @Override
                public void onUIRestored() {}
            };

            RecorderFakeUtils.addScreenshotDetectionCallback(testCallback);

            // Test manual triggers (should all be detected)
            for (int i = 0; i < accuracyTests; i++) {
                RecorderFakeUtils.manuallyTriggerScreenshotHiding();
                Thread.sleep(100); // Wait for detection
                RecorderFakeUtils.manuallyRestoreUIFromScreenshot();
                Thread.sleep(50); // Wait for restoration
            }

            // Wait for all detections
            boolean completed = latch.await(10, TimeUnit.SECONDS);

            double accuracy = completed ? (double) truePositives.get() / accuracyTests * 100 : 0;

            Log.i(TAG, "Screenshot detection accuracy test results:");
            Log.i(TAG, "  Accuracy tests: " + accuracyTests);
            Log.i(TAG, "  True positives: " + truePositives.get());
            Log.i(TAG, "  False positives: " + falsePositives.get());
            Log.i(TAG, "  Accuracy: " + String.format("%.1f", accuracy) + "%");
            Log.i(TAG, "  Completed: " + completed);

            boolean accuracyTestPassed = accuracy >= 95.0 && completed;
            Log.i(TAG, "Screenshot detection accuracy test: " + (accuracyTestPassed ? "PASSED" : "FAILED"));

            // Cleanup
            RecorderFakeUtils.removeScreenshotDetectionCallback(testCallback);
            RecorderFakeUtils.stopScreenshotDetection(context);

        } catch (Exception e) {
            Log.e(TAG, "Screenshot detection accuracy test failed", e);
        }
    }

    /**
     * Test screenshot detection with concurrent operations
     */
    private static void testScreenshotConcurrentOperations(Context context) {
        Log.i(TAG, "=== Screenshot Detection Concurrent Operations Test ===");

        try {
            // Initialize screenshot detection
            RecorderFakeUtils.initializeScreenshotDetection(context);

            final int threadCount = 5;
            final int operationsPerThread = 20;
            final CountDownLatch latch = new CountDownLatch(threadCount);
            final AtomicInteger successCount = new AtomicInteger(0);
            final AtomicInteger errorCount = new AtomicInteger(0);

            long startTime = SystemClock.elapsedRealtime();

            // Create multiple threads performing concurrent screenshot operations
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                new Thread(() -> {
                    try {
                        for (int j = 0; j < operationsPerThread; j++) {
                            try {
                                // Test concurrent screenshot detection
                                RecorderFakeUtils.manuallyTriggerScreenshotHiding();
                                Thread.sleep(10);
                                RecorderFakeUtils.manuallyRestoreUIFromScreenshot();
                                Thread.sleep(10);

                                successCount.incrementAndGet();

                            } catch (Exception e) {
                                errorCount.incrementAndGet();
                                Log.w(TAG, "Thread " + threadId + " operation " + j + " failed", e);
                            }
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        latch.countDown();
                    }
                }).start();
            }

            // Wait for all threads to complete
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            long totalTime = SystemClock.elapsedRealtime() - startTime;

            Log.i(TAG, "Screenshot concurrent operations test results:");
            Log.i(TAG, "  Threads: " + threadCount);
            Log.i(TAG, "  Operations per thread: " + operationsPerThread);
            Log.i(TAG, "  Total operations: " + (threadCount * operationsPerThread));
            Log.i(TAG, "  Successful operations: " + successCount.get());
            Log.i(TAG, "  Failed operations: " + errorCount.get());
            Log.i(TAG, "  Total time: " + totalTime + "ms");
            Log.i(TAG, "  Completed: " + completed);

            boolean concurrencyTestPassed = completed && errorCount.get() < (threadCount * operationsPerThread * 0.05); // Allow 5% error rate
            Log.i(TAG, "Screenshot concurrent operations test: " + (concurrencyTestPassed ? "PASSED" : "FAILED"));

            // Cleanup
            RecorderFakeUtils.stopScreenshotDetection(context);

        } catch (Exception e) {
            Log.e(TAG, "Screenshot concurrent operations test failed", e);
        }
    }
}
