package com.bearmod.loader;


import static android.graphics.Typeface.*;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.Paint;


import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.os.Process;
import android.os.Vibrator;
import android.util.TypedValue;
import android.content.Context;

import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.CompoundButton;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Space;
import android.widget.Switch;
import android.widget.TextView;
import android.util.Base64;
import android.util.Log;
import android.view.Display;
import android.graphics.Rect;
import android.view.WindowMetrics;
import android.os.VibratorManager;
import android.os.VibrationEffect;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.ref.WeakReference;

import android.app.ActivityManager;

import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.IBinder;

import android.graphics.Point;
import android.util.DisplayMetrics;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import android.app.Service;
import android.content.SharedPreferences;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.os.Handler;

import android.view.WindowManager.LayoutParams;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.android.material.tabs.TabLayout;
import com.bearmod.loader.auth.BearModAuthManager;
// import com.bearmod.BuildConfig; // Removed - BuildConfig not available


/**
 * Optimized Floating Service for BearMod
 * - Enhanced memory management and thread safety
 * - Improved resource cleanup and error handling
 * - Better integration with LanguageManager
 * - Gaming-optimized performance
 */
public class Floating extends Service {
    private static final String TAG = "BearMod.Floating";

    // Thread-safe singleton instance management
    private static volatile WeakReference<Floating> instanceRef;

    // Thread-safe configuration management
    private static final Map<String, String> configMap = new ConcurrentHashMap<>();

    // Service lifecycle management
    private final AtomicBoolean isServiceRunning = new AtomicBoolean(false);
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);

    // UI Components - using weak references where appropriate
    private WindowManager windowManager;
    private View floatingView;
    private TabLayout tabLayout;

    // Authentication manager
    private BearModAuthManager authManager;

    // Constants
    public static final int REQUEST_OVERLAY_PERMISSION = 5469;
    private static final int MENU_WIDTH_DP = 300;
    private static final int MENU_HEIGHT_DP = 400;
    private static final int ICON_SIZE_DP = 64;

    int screenWidth, screenHeight, type, CheckAttY = 0;
    boolean EnableFakeRecord = false;
    GradientDrawable gdMenuBody, gdAnimation = new GradientDrawable();
    LayoutParams layoutParams;

    public static LayoutParams iconLayoutParams, mainLayoutParams, canvasLayoutParams;
    public static LayoutParams vParams;
    //  public static  View vTouch;

    private String TimeHari;
    private String TimeJam;
    private String TimeMenit;
    private String TimeDetik;

    public static RelativeLayout iconLayout;
    public static LinearLayout mainLayout, bodyLayout;
    public static ESPView canvasLayout;

    TextView textTitle;
    RelativeLayout closeLayout, maximizeLayout, minimizeLayout;
    RelativeLayout.LayoutParams closeLayoutParams, maximizeLayoutParams, minimizeLayoutParams;

    //  private int EngChIndex = 0;//1
    private int EspMenuTextthi = 0;
    private static final int currentLanguageIndex = 0;
    private LinearLayout contentLayout;


    private native String ChannelName();

    private native String FeedBackName();

    private native String channellink();

    private native String feedbacklink();

    private native String onlinename();

    public native void Switch(int i);

    public static native void DrawOn(ESPView espView, Canvas canvas);


    ImageView iconImg;

    private native String iconenc();

    private final List<LinearLayout> scrollBarContainers = new ArrayList<>();
    private final List<TextView> textViewList = new ArrayList<>();
    private final List<TextView> textViewList2 = new ArrayList<>();
    public static List<TextView> textViewList3 = new ArrayList<>();
    private final List<View> LineViewList = new ArrayList<>();
    private final List<GradientDrawable> comboViewList = new ArrayList<>();

    public static boolean DarkMode;
    private ScrollView scrollView;
    private LinearLayout pageLayout;
    private final int c_Background = Color.argb(255, 242, 241, 247);
    private final int c_Text = Color.BLACK;
    private final int c_Child = Color.WHITE;
    private final int c_Text2 = Color.argb(240, 143, 143, 142);
    private final int c_Line = Color.argb(80, 150, 150, 150);
    public static int c_WidgetsText = Color.BLACK;
    private final int c_Combo = Color.WHITE;

    public static boolean LanguageTest;
    //public static boolean MENULANGUAGE;


    String[] listTab = {"ESP", "ITEMS", "AIM", "SKIN"};
    LinearLayout[] pageLayouts = new LinearLayout[listTab.length];
    LayoutParams params;

    int lastSelectedPage = 0;
    static boolean isBullet;
    boolean CheckAtt;
    SharedPreferences configPrefs;


    int ToggleON = Color.WHITE;
    int ToggleOFF = Color.LTGRAY;
    boolean isMaximized = false;
    int lastMaximizedX = 0, lastMaximizedY = 0;
    int lastMaximizedW = 0, lastMaximizedH = 0;
    int action;
    int layoutWidth;
    int layoutHeight;
    int iconSize;
    int iconSize2;
    int menuButtonSize;
    int tabWidth;
    int tabHeight;

    int MENU_TEXT_COLOR = Color.parseColor("#FFFFFFFF");
    int MENU_LIST_STROKE = Color.argb(255, 200, 100, 0);

    private native boolean IsHideEsp();

    private boolean SaveKey;
    int RadioColor = Color.parseColor("#FFFF9700");
    int MENU_BG_COLOR = Color.parseColor("#fff7f7f7"); // #AARRGGBB

    static boolean isHIDE;
    int Storage_Permission = 142;
    TextView mTitle;

    float mediumSize = 5.0f;

    private native String cfg();

    private native void onSendConfig(String s, String v);

    private Thread thread;
    private boolean isRunning;
    private Paint paint;
    private long startTime;
    private int frames;

    long days;
    long hours;
    long minutes;
    long seconds;

    public static Context g_context;
    private final int fpsViewId = View.generateViewId();
    private final long lastTime = 0;
    private final int frameCount = 0;
    private TextView fpsTextView;


    public static void hideesp() {
        Floating instance = getInstance();
        if (instance != null && instance.windowManager != null) {
            RecorderFakeUtils.setFakeRecorderWindowLayoutParams(mainLayoutParams, iconLayoutParams, canvasLayoutParams, instance.windowManager, mainLayout, iconLayout, canvasLayout, g_context);
        }
    }

    public static void stopHideesp() {
        Floating instance = getInstance();
        if (instance != null && instance.windowManager != null) {
            RecorderFakeUtils.unsetFakeRecorderWindowLayoutParams(mainLayoutParams, iconLayoutParams, canvasLayoutParams, instance.windowManager, mainLayout, iconLayout, canvasLayout, g_context);
        }
    }

    private Boolean GetBoolean(String str) {
        boolean z = configMap.get(str) != null && Integer.parseInt(Objects.requireNonNull(configMap.get(str))) == 1;
        return z;
    }

    private Integer GetInteger(String str) {
        return configMap.get(str) != null ? Integer.parseInt(Objects.requireNonNull(configMap.get(str))) : 0;
    }

    Date time;
    SimpleDateFormat formatter;
    SimpleDateFormat formatter2;

    void CreateCanvas() {
        canvasLayoutParams = new LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT,
                getLayoutType(),
                LayoutParams.FLAG_NOT_FOCUSABLE | LayoutParams.FLAG_NOT_TOUCHABLE | LayoutParams.FLAG_NOT_TOUCH_MODAL,
                PixelFormat.TRANSLUCENT);
        canvasLayoutParams.gravity = Gravity.TOP | Gravity.START;
        canvasLayoutParams.x = 0;
        canvasLayoutParams.y = 0;
        if (Build.VERSION.SDK_INT >= 30) {
            canvasLayoutParams.layoutInDisplayCutoutMode = LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        }
        canvasLayout = new ESPView(this);
        windowManager.addView(canvasLayout, canvasLayoutParams);
    }

    public int getLayoutType() {
        int LAYOUT_FLAG;

        LAYOUT_FLAG = LayoutParams.FLAG_NOT_FOCUSABLE |
                LayoutParams.FLAG_NOT_TOUCHABLE |
                LayoutParams.FLAG_LAYOUT_NO_LIMITS |
                LayoutParams.FLAG_LAYOUT_IN_SCREEN;

        LAYOUT_FLAG = LayoutParams.TYPE_APPLICATION_OVERLAY;
        return LAYOUT_FLAG;
    }

    private boolean isNotInGame() {
        ActivityManager.RunningAppProcessInfo runningAppProcessInfo = new ActivityManager.RunningAppProcessInfo();
        ActivityManager.getMyMemoryState(runningAppProcessInfo);
        return runningAppProcessInfo.importance != 100;
    }

    private void Thread() {
        WindowManager windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        if (isNotInGame()) {
            try {
                if (windowManager != null) {
                    windowManager.removeView(mainLayout);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {

            mainLayoutParams = new LayoutParams(layoutWidth, layoutHeight, LayoutParams.TYPE_APPLICATION_OVERLAY, LayoutParams.FLAG_NOT_FOCUSABLE, PixelFormat.TRANSLUCENT);

            try {
                windowManager.addView(mainLayout, mainLayoutParams);
            } catch (Exception e) {
                // e.printStackTrace();
            }
        }
    }

    private void LoadConfiguration(/*String customPat*/) {
        try {
            File file;

            file = new File(getFilesDir(), "NRG_SaveFile.cfg");
            BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
            String readLine;
            while ((readLine = bufferedReader.readLine()) != null) {
                String[] split = readLine.split(" = ");
                if (split.length == 2) {
                    configMap.put(split[0], split[1]);
                }
            }
            bufferedReader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void onSendConfig(String split, Object parseInt) {
        configMap.put(split, parseInt.toString());
        // onSendConfig(split, parseInt.toString());
        parseInt.toString();
        configMap.put(split, String.valueOf(parseInt));
    }

    public void SaveConfiguration(/*String customPath*/) {
        try {
            File file;

            file = new File(getFilesDir(), "NRG_SaveFile.cfg");
            //    }

            PrintWriter printWriter = new PrintWriter(new FileOutputStream(file), true);
            for (Map.Entry<String, String> entry : configMap.entrySet()) {
                printWriter.println(entry.getKey() + " = " + entry.getValue());
            }
            printWriter.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void UpdateConfiguration2(String s, Object v) {
        try {
            configMap.put(s, v.toString());
            onSendConfig(s, v.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void UpdateConfiguration(String s, Object v) {
        try {
            configMap.put(s, v.toString()); // Update local configMap
            onSendConfig(s, v.toString()); // Send to native if needed

            SharedPreferences.Editor configEditor = configPrefs.edit();
            configEditor.putString(s, v.toString());
            configEditor.apply();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private class CanvasView extends View {
        public CanvasView(Context context) {
            super(context);

            g_context = context;

        }


    }

    private int seconds2 = 0;

    @Override
    public void onDestroy() {
        Log.d(TAG, "Service destroying - cleaning up resources");

        // Set service state
        isServiceRunning.set(false);

        try {
            // Clean up LanguageManager listeners
            cleanupLanguageManager();

            // Stop and clean up threads
            cleanupThreads();

            // Clean up UI components and views
            cleanupViews();

            // Clean up native resources
            cleanupNativeResources();

            // Clear instance reference
            synchronized (Floating.class) {
                if (instanceRef != null) {
                    instanceRef.clear();
                    instanceRef = null;
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error during service cleanup", e);
        } finally {
            super.onDestroy();
            Log.d(TAG, "Service destroyed successfully");
        }
    }

    /**
     * Get screen size using modern APIs with fallback for older versions
     */
    @SuppressWarnings("deprecation")
    private Point getScreenSize() {
        Point size = new Point();

        // Safety check for windowManager
        if (windowManager == null) {
            Log.w(TAG, "WindowManager is null, using fallback screen size");
            size.x = 1080; // Default fallback
            size.y = 1920;
            return size;
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ (API 30+) - Use WindowMetrics
                WindowMetrics windowMetrics = windowManager.getCurrentWindowMetrics();
                Rect bounds = windowMetrics.getBounds();
                size.x = bounds.width();
                size.y = bounds.height();
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                // Android 4.2+ (API 17+) - Use getRealSize (deprecated but needed for compatibility)
                Display display = windowManager.getDefaultDisplay();
                display.getRealSize(size);
            } else {
                // Fallback for very old versions
                Display display = windowManager.getDefaultDisplay();
                size.x = display.getWidth();
                size.y = display.getHeight();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting screen size, using fallback", e);
            size.x = 1080; // Default fallback
            size.y = 1920;
        }

        return size;
    }

    /**
     * Get display metrics using modern APIs with fallback
     */
    @SuppressWarnings("deprecation")
    private DisplayMetrics getDisplayMetrics() {
        DisplayMetrics metrics = new DisplayMetrics();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            Display display = windowManager.getDefaultDisplay();
            display.getRealMetrics(metrics);
        } else {
            Display display = windowManager.getDefaultDisplay();
            display.getMetrics(metrics);
        }

        return metrics;
    }

    /**
     * Perform legacy vibration with proper deprecation suppression
     */
    @SuppressWarnings("deprecation")
    private void performLegacyVibration() {
        try {
            Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    VibrationEffect vibrationEffect = VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE);
                    vibrator.vibrate(vibrationEffect);
                } else {
                    // Fallback for older devices
                    vibrator.vibrate(50);
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "Error performing vibration", e);
        }
    }

    /**
     * Clean up LanguageManager listeners to prevent memory leaks
     */
    private void cleanupLanguageManager() {
        try {
            LanguageManager.getInstance().removeLanguageChangeListener("floating_ui");
            Log.d(TAG, "LanguageManager listeners cleaned up");
        } catch (Exception e) {
            Log.w(TAG, "Error cleaning up LanguageManager", e);
        }
    }

    /**
     * Safely stop and clean up background threads
     */
    private void cleanupThreads() {
        try {
            // Stop canvas update thread
            if (mUpdateCanvas != null && mUpdateCanvas.isAlive()) {
                mUpdateCanvas.interrupt();
                try {
                    mUpdateCanvas.join(1000); // Wait up to 1 second
                } catch (InterruptedException e) {
                    Log.w(TAG, "Canvas thread cleanup interrupted", e);
                    Thread.currentThread().interrupt();
                }
            }

            // Stop main update thread
            if (mUpdateThread != null && mUpdateThread.isAlive()) {
                mUpdateThread.interrupt();
                try {
                    mUpdateThread.join(1000); // Wait up to 1 second
                } catch (InterruptedException e) {
                    Log.w(TAG, "Main thread cleanup interrupted", e);
                    Thread.currentThread().interrupt();
                }
            }

            Log.d(TAG, "Background threads cleaned up");
        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up threads", e);
        }
    }

    /**
     * Safely remove all views from WindowManager
     */
    private void cleanupViews() {
        if (windowManager == null) return;

        try {
            // Remove floating view
            safeRemoveView(floatingView, "floatingView");

            // Remove icon layout
            safeRemoveView(iconLayout, "iconLayout");

            // Remove main layout
            safeRemoveView(mainLayout, "mainLayout");

            // Remove canvas layout
            safeRemoveView(canvasLayout, "canvasLayout");

            // Clean up bitmap resources
            cleanupBitmaps();

            Log.d(TAG, "Views cleaned up successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up views", e);
        }
    }

    /**
     * Safely remove a view from WindowManager
     */
    private void safeRemoveView(View view, String viewName) {
        if (view != null && view.getParent() != null) {
            try {
                windowManager.removeView(view);
                Log.d(TAG, viewName + " removed successfully");
            } catch (Exception e) {
                Log.w(TAG, "Error removing " + viewName, e);
            }
        }
    }

    /**
     * Clean up bitmap resources to prevent memory leaks
     */
    private void cleanupBitmaps() {
        try {
            if (iconImg != null) {
                Drawable drawable = iconImg.getDrawable();
                if (drawable instanceof android.graphics.drawable.BitmapDrawable) {
                    Bitmap bitmap = ((android.graphics.drawable.BitmapDrawable) drawable).getBitmap();
                    if (bitmap != null && !bitmap.isRecycled()) {
                        bitmap.recycle();
                    }
                }
                iconImg.setImageDrawable(null);
            }
            Log.d(TAG, "Bitmap resources cleaned up");
        } catch (Exception e) {
            Log.w(TAG, "Error cleaning up bitmaps", e);
        }
    }

    /**
     * Clean up native resources
     */
    private void cleanupNativeResources() {
        try {
            // Save configuration before cleanup
            SaveConfiguration();

            // Clear configuration map
            configMap.clear();

            Log.d(TAG, "Native resources cleaned up");
        } catch (Exception e) {
            Log.w(TAG, "Error cleaning up native resources", e);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        super.onStartCommand(intent, flags, startId);
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * Get singleton instance of Floating service
     */
    public static Floating getInstance() {
        if (instanceRef != null) {
            return instanceRef.get();
        }
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Floating service creating...");

        try {
            // Set instance reference
            synchronized (Floating.class) {
                instanceRef = new WeakReference<>(this);
            }

            // Set service state
            isServiceRunning.set(true);

            // Initialize native library with error handling
            if (!initializeNativeLibrary()) {
                Log.e(TAG, "Failed to initialize native library");
                stopSelf();
                return;
            }

            // Initialize core components
            if (!initializeCoreComponents()) {
                Log.e(TAG, "Failed to initialize core components");
                stopSelf();
                return;
            }

            // Initialize UI components
            if (!initializeUIComponents()) {
                Log.e(TAG, "Failed to initialize UI components");
                stopSelf();
                return;
            }

            // Initialize LanguageManager integration
            initializeLanguageManager();

            // Initialize authentication manager
            initializeAuthManager();

            // Start background threads
            startBackgroundThreads();

            // Run performance tests in debug mode
            runDebugTests();

            isInitialized.set(true);
            Log.d(TAG, "Floating service created successfully");

        } catch (Exception e) {
            Log.e(TAG, "Critical error during service creation", e);
            stopSelf();
        }
    }

    /**
     * Initialize native library with proper error handling
     */
    private boolean initializeNativeLibrary() {
        try {
            System.loadLibrary("bearmod");
            Log.d(TAG, "Native library loaded successfully");
            return true;
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library", e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error loading native library", e);
            return false;
        }
    }

    /**
     * Initialize core service components
     */
    private boolean initializeCoreComponents() {
        try {
            // Initialize preferences
            configPrefs = getSharedPreferences("config", MODE_PRIVATE);

            // Initialize window manager
            windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
            if (windowManager == null) {
                Log.e(TAG, "Failed to get WindowManager service");
                return false;
            }

            // Initialize time formatters
            time = new Date();
            formatter = new SimpleDateFormat(" HH:mm:ss", Locale.getDefault());
            formatter2 = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

            // Get display metrics with error handling
            if (!initializeDisplayMetrics()) {
                return false;
            }

            // Initialize dimensions
            initializeDimensions();

            Log.d(TAG, "Core components initialized successfully");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error initializing core components", e);
            return false;
        }
    }

    /**
     * Initialize display metrics with modern API and fallback
     */
    private boolean initializeDisplayMetrics() {
        try {
            // Use the new utility method for screen size
            Point size = getScreenSize();
            screenWidth = size.x;
            screenHeight = size.y;

            Log.d(TAG, "Display metrics: " + screenWidth + "x" + screenHeight);
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error getting display metrics", e);
            return false;
        }
    }

    /**
     * Initialize UI dimensions
     */
    private void initializeDimensions() {
        // Set fixed compact dimensions for all devices
        layoutWidth = MENU_WIDTH_DP;
        layoutHeight = MENU_HEIGHT_DP;
        iconSize = ICON_SIZE_DP;
        iconSize2 = 100;    // Reduced secondary icon size
        menuButtonSize = 25;  // Smaller menu buttons
        tabWidth = 0;
        tabHeight = 0;
        type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;

        // Convert dp values to pixels for actual use
        layoutWidth = dpToPx(layoutWidth);
        layoutHeight = dpToPx(layoutHeight);
        iconSize = dpToPx(iconSize);
        menuButtonSize = dpToPx(menuButtonSize);

        Log.d(TAG, "Dimensions initialized - Layout: " + layoutWidth + "x" + layoutHeight);
    }

    /**
     * Initialize UI components with error handling
     */
    private boolean initializeUIComponents() {
        try {
            // Load configuration
            LoadConfiguration();

            // Create UI components
            CreateCanvas();
            CreateLayout();
            CreateIcon();

            // Ensure mainLayout is properly added
            if (mainLayout != null) {
                if (mainLayout.getParent() != null) {
                    windowManager.removeView(mainLayout);
                }
                windowManager.addView(mainLayout, mainLayoutParams);
                mainLayout.setVisibility(View.GONE); // Start hidden
            }

            // Initialize default configurations
            initDefaultConfigurations();
            setLanguageBasedOnSystemLocale();

            Log.d(TAG, "UI components initialized successfully");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error initializing UI components", e);
            return false;
        }
    }

    /**
     * Initialize LanguageManager integration
     */
    private void initializeLanguageManager() {
        try {
            // Initialize optimized LanguageManager
            LanguageManager.getInstance().initialize(this);

            // Register for language change notifications with error handling
            LanguageManager.getInstance().addLanguageChangeListener("floating_ui",
                newLanguage -> {
                    try {
                        // Update UI when language changes (ensure on UI thread)
                        if (Looper.myLooper() == Looper.getMainLooper()) {
                            translateMenuElements();
                        } else {
                            new Handler(Looper.getMainLooper()).post(this::translateMenuElements);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error updating UI for language change", e);
                    }
                });

            Log.d(TAG, "LanguageManager integration initialized");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing LanguageManager", e);
        }
    }

    /**
     * Initialize authentication manager
     */
    private void initializeAuthManager() {
        try {
            authManager = BearModAuthManager.getInstance(this);
            authManager.initialize(new BearModAuthManager.AuthCallback() {
                @Override
                public void onSuccess(String message) {
                    Log.d(TAG, "Auth manager initialized: " + message);
                    // Check if user is already authenticated
                    if (authManager.isAuthenticated()) {
                        Log.d(TAG, "User already authenticated");
                        // Show main UI
                        showMainUI();
                    } else {
                        Log.d(TAG, "User not authenticated - showing login");
                        // Show login UI
                        showLoginUI();
                    }
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "Auth manager initialization failed: " + error);
                    // Show login UI as fallback
                    showLoginUI();
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error initializing authentication manager", e);
            // Show login UI as fallback
            showLoginUI();
        }
    }

    /**
     * Start background threads with proper error handling
     */
    private void startBackgroundThreads() {
        try {
            // Start canvas update thread
            if (mUpdateCanvas != null && !mUpdateCanvas.isAlive()) {
                mUpdateCanvas.start();
            }

            // Start main update thread
            if (mUpdateThread != null && !mUpdateThread.isAlive()) {
                mUpdateThread.start();
            }

            Log.d(TAG, "Background threads started");

        } catch (Exception e) {
            Log.e(TAG, "Error starting background threads", e);
        }
    }

    /**
     * Run performance tests in debug mode (disabled by default to prevent crashes)
     */
    private void runDebugTests() {
        // Debug tests disabled by default to prevent crashes during login flow
        // Enable only for development/testing purposes
        boolean enableDebugTests = false; // Set to true only for testing

        if (enableDebugTests) {
            new Thread(() -> {
                try {
                    Thread.sleep(5000); // Wait longer for full initialization
                    Log.d(TAG, "Starting debug performance tests");

                    // Only run tests if service is properly initialized
                    if (isInitialized.get() && isServiceRunning.get()) {
                        LanguageManagerTest.runAllTests(this);
                        Log.d(TAG, "Debug performance tests completed");
                    } else {
                        Log.w(TAG, "Skipping debug tests - service not fully initialized");
                    }
                } catch (InterruptedException e) {
                    Log.w(TAG, "Performance test interrupted", e);
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    Log.e(TAG, "Error running performance tests", e);
                } catch (OutOfMemoryError e) {
                    Log.e(TAG, "Out of memory during performance tests", e);
                    // Force garbage collection
                    System.gc();
                }
            }).start();
        } else {
            Log.d(TAG, "Debug tests disabled for stability");
        }
    }

    /**
     * Show login UI for authentication
     */
    private void showLoginUI() {
        try {
            new Handler(Looper.getMainLooper()).post(() -> {
                try {
                    if (mainLayout != null) {
                        // Hide main layout initially
                        mainLayout.setVisibility(View.GONE);
                    }

                    // Create simple login dialog
                    createLoginDialog();

                } catch (Exception e) {
                    Log.e(TAG, "Error showing login UI", e);
                    // Fallback to showing main UI
                    showMainUI();
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error posting login UI to main thread", e);
        }
    }

    /**
     * Show main UI after successful authentication
     */
    private void showMainUI() {
        try {
            new Handler(Looper.getMainLooper()).post(() -> {
                try {
                    if (mainLayout != null) {
                        mainLayout.setVisibility(View.VISIBLE);
                        Log.d(TAG, "Main UI shown successfully");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error showing main UI", e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error posting main UI to main thread", e);
        }
    }

    /**
     * Create simple login dialog
     */
    private void createLoginDialog() {
        try {
            // For now, just show the main UI
            // In production, this would show a proper login form
            Log.d(TAG, "Login dialog would be shown here");

            // Simulate successful authentication for testing
            if (authManager != null) {
                authManager.authenticate("demo_license_key", new BearModAuthManager.AuthCallback() {
                    @Override
                    public void onSuccess(String message) {
                        Log.d(TAG, "Demo authentication successful");
                        showMainUI();
                    }

                    @Override
                    public void onError(String error) {
                        Log.e(TAG, "Demo authentication failed: " + error);
                        // Still show main UI for demo purposes
                        showMainUI();
                    }
                });
            } else {
                // Fallback if auth manager is not available
                showMainUI();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error creating login dialog", e);
            showMainUI();
        }
    }

    private void initDefaultConfigurations() {
        UpdateConfiguration("AIM::TRIGGER1", (byte) 1);
        UpdateConfiguration("AIM::TARGET1", (byte) 1);
        UpdateConfiguration("ESP::BOXTYPE1", (byte) 1);
        UpdateConfiguration("AIM_MOD1", (byte) 1);
        UpdateConfiguration("SMOOT::HNESS1", (byte) 1);
        UpdateConfiguration("RADAR::SIZE", (byte) 60);
    }

    private void setLanguageBasedOnSystemLocale() {
        String deviceLanguage = Locale.getDefault().getLanguage();
        EspMenuTextthi = "zh".equals(deviceLanguage) ? 1 : 0;
    }

    //   private String getEspMenuText(String englishText, String chineseText) {
    // return (EspMenuTextthi == 1) ? chineseText : englishText;
    //  }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        setLanguageBasedOnSystemLocale();
        translateMenuElements();
    }

    public String GetString(String key, String defaultValue) {
        String value = configMap.get(key);
        return value != null ? value : defaultValue;
    }

    String getEspMenuText(String engText, String cnText) {
        switch (EspMenuTextthi) {
            case 0:
                return engText;
            case 1:
                return cnText;

            default:
                return engText;
        }
    }

    private void translateMenuElements() {
        // Use optimized LanguageManager for translations
        LanguageManager langManager = LanguageManager.getInstance();

        // Translate main tabs
        if (textTitle != null) {
            textTitle.setText(getEspMenuText("BearMod", "熊模组"));
        }

        // Tab titles - Using TabLayout if available
        if (tabLayout != null) {
            int tabCount = tabLayout.getTabCount();
            if (tabCount > 0) {
                TabLayout.Tab espTab = tabLayout.getTabAt(0);
                if (espTab != null) espTab.setText(langManager.get("Esp Menu"));
            }
            if (tabCount > 1) {
                TabLayout.Tab aimTab = tabLayout.getTabAt(1);
                if (aimTab != null) aimTab.setText(langManager.get("Aim Menu"));
            }
            if (tabCount > 2) {
                TabLayout.Tab itemsTab = tabLayout.getTabAt(2);
                if (itemsTab != null) itemsTab.setText(getEspMenuText("ITEMS", "物品"));
            }
            if (tabCount > 3) {
                TabLayout.Tab skinTab = tabLayout.getTabAt(3);
                if (skinTab != null) skinTab.setText(langManager.get("Skin Menu"));
            }
            if (tabCount > 4) {
                TabLayout.Tab hideTab = tabLayout.getTabAt(4);
                if (hideTab != null) hideTab.setText(langManager.get("Hide Menu"));
            }
        }

        // Button texts
        if (closeLayout != null) {
            closeLayout.setContentDescription(getEspMenuText("Close", "关闭"));
        }
        if (minimizeLayout != null) {
            minimizeLayout.setContentDescription(getEspMenuText("Minimize", "最小化"));
        }
        if (maximizeLayout != null) {
            maximizeLayout.setContentDescription(getEspMenuText("Maximize", "最大化"));
        }

        // Update settings text if present
        if (mTitle != null) {
            mTitle.setText(langManager.get("Settings"));
        }

        Log.d("Floating", "UI elements translated using optimized LanguageManager");
    }


    @SuppressLint({"ClickableViewAccessibility", "SetTextI18n"})
    void CreateLayout() {
        LoadConfiguration();

        int windowType = LayoutParams.TYPE_APPLICATION_OVERLAY;
        mainLayoutParams = new LayoutParams(
                layoutWidth,
                layoutHeight,
                windowType,
                LayoutParams.FLAG_NOT_FOCUSABLE |
                        LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.TRANSLUCENT
        );
        mainLayoutParams.gravity = Gravity.START | Gravity.TOP;
        mainLayoutParams.x = 50;  // Initial X position
        mainLayoutParams.y = 50;  // Initial Y position


        //   if (configMap.containsKey("SETTING_MENU")) {

        mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        mainLayout.setBackgroundColor(Color.parseColor("#383838"));

        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, this.dp(30)));
        headerLayout.setOrientation(LinearLayout.VERTICAL);
        headerLayout.setPadding(10, 10, 10, 10);
        headerLayout.setClickable(true);
        headerLayout.setFocusable(true);
        headerLayout.setFocusableInTouchMode(true);

        mainLayout.addView(headerLayout);
        GradientDrawable drawable = new GradientDrawable();
        drawable.setColor(Color.parseColor("#383838"));
        drawable.setStroke(2, MENU_TEXT_COLOR);
        drawable.setCornerRadius(20);
        mainLayout.setBackground(drawable);

        new GestureDetector(Floating.this, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                mainLayout.setVisibility(View.GONE);
                iconLayout.setVisibility(View.VISIBLE);
                return super.onDoubleTap(e);
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                Toast.makeText(Floating.this, "Double tap to close the menu.", Toast.LENGTH_LONG).show();
                return super.onSingleTapConfirmed(e);
            }
        });
        View.OnTouchListener onTitleListener = new View.OnTouchListener() {
            float pressedX;
            float pressedY;
            float deltaX;
            float deltaY;
            float newX;
            float newY;
            float maxX;
            float maxY;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getActionMasked()) {
                    case MotionEvent.ACTION_DOWN:
                        pressedX = event.getRawX();
                        pressedY = event.getRawY();
                        deltaX = mainLayoutParams.x;
                        deltaY = mainLayoutParams.y;
                        maxX = screenWidth - mainLayout.getWidth();
                        maxY = screenHeight - mainLayout.getHeight();
                        break;
                    case MotionEvent.ACTION_MOVE:
                        newX = event.getRawX() - pressedX + deltaX;
                        newY = event.getRawY() - pressedY + deltaY;
                        mainLayoutParams.x = (int) Math.max(0, Math.min(newX, maxX));
                        mainLayoutParams.y = (int) Math.max(0, Math.min(newY, maxY));
                        windowManager.updateViewLayout(mainLayout, mainLayoutParams);
                        break;
                }
                return false;
            }
        };

        TextView textView = new TextView(this);
        textView.setGravity(Gravity.CENTER_HORIZONTAL);
        textView.setTextSize(16);
        textView.setText(onlinename());
        textView.setTextColor(Color.WHITE);
        headerLayout.addView(textView);
        headerLayout.setOnTouchListener(onTitleListener);
        textView.setOnTouchListener(onTitleListener);

        final LinearLayout main = new LinearLayout(this);
        main.setOrientation(LinearLayout.HORIZONTAL);
        main.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        final LinearLayout horbar = new LinearLayout(this);
        horbar.setLayoutParams(new ViewGroup.LayoutParams(dp(110), ViewGroup.LayoutParams.WRAP_CONTENT));
        horbar.setBackgroundColor(Color.parseColor("#303030"));
        horbar.setOrientation(LinearLayout.VERTICAL);

        GradientDrawable drawable2 = new GradientDrawable();
        drawable2.setColor(Color.parseColor("#303030"));
        drawable2.setStroke(2, MENU_TEXT_COLOR);
        drawable2.setCornerRadius(20);
        horbar.setBackground(drawable2);

        final LinearLayout vbar = new LinearLayout(this);
        vbar.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        final ScrollView hrscroll = new ScrollView(this);
        hrscroll.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        hrscroll.setHorizontalScrollBarEnabled(false);
        final LinearLayout scrollbg = new LinearLayout(this);
        scrollbg.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        scrollbg.setGravity(Gravity.CENTER_HORIZONTAL);
        scrollbg.setOrientation(LinearLayout.VERTICAL);

        GradientDrawable tabson = new GradientDrawable();
        tabson.setColor(Color.parseColor("#524E4E"));
        tabson.setCornerRadius(5);

        GradientDrawable sl = new GradientDrawable();
        sl.setColor(Color.GREEN);
        sl.setCornerRadius(dp(50));
        final LinearLayout ball = new LinearLayout(this);
        LinearLayout.LayoutParams params2 = new LinearLayout.LayoutParams(this.convertSizeToDp(6), this.convertSizeToDp(6));
        params2.setMargins(convertSizeToDp(2.5f), convertSizeToDp(2.5f), convertSizeToDp(2.5f), convertSizeToDp(2.5f));
        ball.setLayoutParams(params2);
        ball.setBackground(sl);
        ball.setGravity(5);

        final LinearLayout ballc = new LinearLayout(this);
        ballc.setLayoutParams(params2);
        ballc.setBackground(sl);
        ballc.setGravity(5);

        final LinearLayout balle = new LinearLayout(this);
        balle.setLayoutParams(params2);
        balle.setBackground(sl);
        balle.setGravity(5);

        final LinearLayout balls = new LinearLayout(this);
        balls.setLayoutParams(params2);
        balls.setBackground(sl);
        balls.setGravity(5);

        final LinearLayout balla = new LinearLayout(this);
        balla.setLayoutParams(params2);
        balla.setBackground(sl);
        balla.setGravity(5);

        final LinearLayout tabBypass = new LinearLayout(this);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, this.convertSizeToDp(40));
        params.setMargins(convertSizeToDp(2.5f), convertSizeToDp(2.5f), convertSizeToDp(2.5f), convertSizeToDp(2.5f));
        tabBypass.setLayoutParams(params);
        tabBypass.setBackground(tabson);
        tabBypass.setOrientation(LinearLayout.HORIZONTAL);
        tabBypass.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);

        TextView txtMain = new TextView(this);
        txtMain.setText(getEspMenuText("Main Menu", "主菜单"));
        txtMain.setTextSize(12);
        txtMain.setShadowLayer(10, 1, 1, Color.BLACK);
        txtMain.setTextColor(Color.WHITE);

        final LinearLayout tabMain = new LinearLayout(this);
        tabMain.setLayoutParams(params);
        tabMain.setBackground(tabson);
        tabMain.setOrientation(LinearLayout.HORIZONTAL);
        tabMain.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);


        TextView txtEsp = new TextView(this);
        txtEsp.setText(getEspMenuText("Esp Menu", "透视菜单"));
        txtEsp.setTextSize(12);
        txtEsp.setShadowLayer(10, 1, 1, Color.BLACK);
        txtEsp.setTextColor(Color.WHITE);

        final LinearLayout tabEsp = new LinearLayout(this);
        tabEsp.setLayoutParams(params);
        tabEsp.setBackground(tabson);
        tabEsp.setOrientation(LinearLayout.HORIZONTAL);
        tabEsp.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);

        TextView txtAim = new TextView(this);
        txtAim.setText(getEspMenuText("Aim Menu", "瞄准菜单"));
        txtAim.setTextSize(12);
        txtAim.setTextColor(Color.WHITE);
        txtAim.setShadowLayer(10, 1, 1, Color.BLACK);

        final LinearLayout tabAim = new LinearLayout(this);
        tabAim.setLayoutParams(params);
        tabAim.setBackground(tabson);
        tabAim.setOrientation(LinearLayout.HORIZONTAL);
        tabAim.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);

        TextView txtSkin = new TextView(this);
        txtSkin.setText(getEspMenuText("Skin Menu", "皮肤菜单"));
        txtSkin.setTextSize(12);
        txtSkin.setShadowLayer(10, 1, 1, Color.BLACK);
        txtSkin.setTextColor(Color.WHITE);

        final LinearLayout tabSkin = new LinearLayout(this);
        tabSkin.setLayoutParams(params);
        tabSkin.setBackground(tabson);
        tabSkin.setOrientation(LinearLayout.HORIZONTAL);
        tabSkin.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);

        TextView txtHide = new TextView(this);
        txtHide.setText("Hide Menu");
        txtHide.setTextSize(12);
        txtHide.setShadowLayer(10, 1, 1, Color.BLACK);
        txtHide.setTextColor(Color.WHITE);

        final LinearLayout tabHide = new LinearLayout(this);
        tabHide.setLayoutParams(params);
        tabHide.setBackground(tabson);
        tabHide.setOrientation(LinearLayout.HORIZONTAL);
        tabHide.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);
        tabHide.setVisibility(View.GONE);

        final ScrollView sc = new ScrollView(this);
        sc.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        final LinearLayout container = new LinearLayout(this);
        container.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        container.setOrientation(LinearLayout.VERTICAL);

        final LinearLayout layoutBypasss = new LinearLayout(this);
        layoutBypasss.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layoutBypasss.setOrientation(LinearLayout.VERTICAL);
        layoutBypasss.setPadding(15, 15, 15, 15);

        final LinearLayout layoutEsp = new LinearLayout(this);
        layoutEsp.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layoutEsp.setOrientation(LinearLayout.VERTICAL);
        layoutEsp.setPadding(15, 15, 15, 15);

        LayoutParams tabparam2 = new LayoutParams(dp(100), dp(40));

        final LinearLayout layoutCheat = new LinearLayout(this);
        layoutCheat.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layoutCheat.setOrientation(LinearLayout.VERTICAL);
        layoutCheat.setPadding(15, 15, 15, 15);

        final LinearLayout layoutSet = new LinearLayout(this);
        layoutSet.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layoutSet.setOrientation(LinearLayout.VERTICAL);
        layoutSet.setPadding(15, 15, 15, 15);

        final LinearLayout layoutAcc = new LinearLayout(this);
        layoutAcc.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layoutAcc.setOrientation(LinearLayout.VERTICAL);
        layoutAcc.setPadding(15, 15, 15, 15);

        mainLayout.addView(main);
        main.addView(horbar);
        main.addView(vbar);
        horbar.addView(hrscroll);
        hrscroll.addView(scrollbg);
        vbar.addView(sc);
        sc.addView(container);
        container.addView(layoutBypasss);

        scrollbg.addView(tabBypass);
        scrollbg.addView(tabMain);
        scrollbg.addView(tabEsp);
        scrollbg.addView(tabAim);
        scrollbg.addView(tabSkin);

        tabBypass.addView(txtMain);
        tabBypass.addView(ball);

        tabMain.addView(txtEsp);
        tabEsp.addView(txtAim);
        tabAim.addView(txtSkin);
        tabSkin.addView(txtHide);

        tabMain.addView(balle);
        tabEsp.addView(ballc);
        tabAim.addView(balls);
        tabSkin.addView(balla);

        ball.setVisibility(View.VISIBLE);
        ballc.setVisibility(View.GONE);
        balla.setVisibility(View.GONE);
        balls.setVisibility(View.GONE);
        balle.setVisibility(View.GONE);

        tabBypass.setOnClickListener(view -> {
            ball.setVisibility(View.VISIBLE);
            ballc.setVisibility(View.GONE);
            balla.setVisibility(View.GONE);
            balls.setVisibility(View.GONE);
            balle.setVisibility(View.GONE);
            container.removeAllViews();
            container.addView(layoutBypasss);

            if (contentLayout != null) {
                contentLayout.removeAllViews();
            }
        });

        tabMain.setOnClickListener(view -> {
            ball.setVisibility(View.GONE);
            ballc.setVisibility(View.GONE);
            balla.setVisibility(View.GONE);
            balls.setVisibility(View.GONE);
            balle.setVisibility(View.VISIBLE);
            container.removeAllViews();
            container.addView(layoutEsp);

            if (contentLayout != null) {
                contentLayout.removeAllViews();
            }
        });

///fix
        tabEsp.setOnClickListener(view -> {
            ball.setVisibility(View.GONE);
            ballc.setVisibility(View.VISIBLE);
            balla.setVisibility(View.GONE);
            balls.setVisibility(View.GONE);
            balle.setVisibility(View.GONE);
            //   ballh.setVisibility(View.GONE);
            container.removeAllViews();
            container.addView(layoutCheat);

        });
        tabAim.setOnClickListener(view -> {
            ball.setVisibility(View.GONE);
            ballc.setVisibility(View.GONE);
            balla.setVisibility(View.GONE);
            balls.setVisibility(View.VISIBLE);
            balle.setVisibility(View.GONE);
            //    ballh.setVisibility(View.GONE);
            container.removeAllViews();
            container.addView(layoutSet);

        });
        tabSkin.setOnClickListener(view -> {
            ball.setVisibility(View.VISIBLE);
            ballc.setVisibility(View.GONE);
            balla.setVisibility(View.GONE);
            balls.setVisibility(View.GONE);
            balle.setVisibility(View.GONE);
            container.removeAllViews();
            //container.addView(layoutAcc);
            container.addView(layoutBypasss);
            mainLayout.setVisibility(View.GONE);
            iconLayout.setVisibility(View.VISIBLE);

        });

        tabHide.setOnClickListener(v -> {

            mainLayout.setVisibility(View.GONE);
            iconLayout.setVisibility(View.VISIBLE);

        });


        // Add view with error handling for UI thread safety
        try {
            if (windowManager != null && mainLayout != null && mainLayoutParams != null) {
                windowManager.addView(mainLayout, mainLayoutParams);
                Log.d(TAG, "Main layout added to window manager successfully");
            } else {
                Log.e(TAG, "Cannot add main layout - null components detected");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding main layout to window manager", e);
            // Try to recover by recreating the layout parameters
            try {
                if (mainLayout != null && windowManager != null) {
                    mainLayoutParams = new LayoutParams(
                            layoutWidth,
                            layoutHeight,
                            LayoutParams.TYPE_APPLICATION_OVERLAY,
                            LayoutParams.FLAG_NOT_FOCUSABLE | LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                            PixelFormat.TRANSLUCENT
                    );
                    mainLayoutParams.gravity = Gravity.START | Gravity.TOP;
                    mainLayoutParams.x = 50;
                    mainLayoutParams.y = 50;
                    windowManager.addView(mainLayout, mainLayoutParams);
                    Log.d(TAG, "Main layout added successfully after recovery");
                }
            } catch (Exception recoveryException) {
                Log.e(TAG, "Failed to recover main layout", recoveryException);
            }
        }

        // Restore language selection
        if (configMap.containsKey("SETTING_MENU")) {
            EspMenuTextthi = GetInteger("SETTING_MENU");
        }

        String[] languages = {"English", "中国人"};
        LinearLayout languageContainer = new LinearLayout(this);
        languageContainer.setOrientation(LinearLayout.VERTICAL);
        languageContainer.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));

        RadioGroup radioGroup = new RadioGroup(this);
        radioGroup.setOrientation(RadioGroup.VERTICAL);
        radioGroup.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));

        for (int i = 0; i < languages.length; i++) {
            RadioButton radioButton = new RadioButton(this);
            radioButton.setText(languages[i]);
            radioButton.setId(View.generateViewId());
            radioButton.setChecked(GetInteger("SETTING_MENU") == i);
            radioButton.setTextColor(Color.WHITE);
            radioButton.setLayoutParams(new RadioGroup.LayoutParams(
                    RadioGroup.LayoutParams.MATCH_PARENT,
                    RadioGroup.LayoutParams.WRAP_CONTENT));
            radioGroup.addView(radioButton);
        }

        radioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            for (int i = 0; i < languages.length; i++) {
                if (group.getChildAt(i).getId() == checkedId) {
                    EspMenuTextthi = i;
                    // Use optimized LanguageManager for language switching
                    LanguageManager.getInstance().setLanguage(i);
                    UpdateConfiguration2("SETTING_MENU", i);
                    SaveConfiguration();
                    // translateMenuElements() will be called automatically via listener
                    break;
                }
            }
        });

        languageContainer.addView(radioGroup);
        
        // Add title for language selection
        TextView languageTitle = new TextView(this);
        languageTitle.setText(getEspMenuText("Language", "语言"));
        languageTitle.setTextSize(14);
        languageTitle.setTextColor(Color.WHITE);
        languageTitle.setTypeface(android.graphics.Typeface.DEFAULT_BOLD);
        languageTitle.setPadding(dpToPx(10), dpToPx(10), dpToPx(10), dpToPx(5));
        languageTitle.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));
        
        // Add divider
        View divider = new View(this);
        divider.setBackgroundColor(Color.parseColor("#FFFFFF"));
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                1);
        dividerParams.setMargins(dpToPx(20), dpToPx(5), dpToPx(20), dpToPx(10));
        divider.setLayoutParams(dividerParams);

        // Add components in correct order
        layoutBypasss.addView(languageTitle);
        layoutBypasss.addView(languageContainer);
        layoutBypasss.addView(divider);

        // Add spacing after language section
        addSpacing(layoutBypasss, 10);

        addCheckboxWithSwitch(layoutBypasss, (buttonView, isChecked) -> {
            if (isChecked) {
                RecorderFakeUtils.setFakeRecorderWindowLayoutParams(mainLayoutParams, iconLayoutParams, canvasLayoutParams, windowManager, mainLayout, iconLayout, canvasLayout, Floating.this);
            } else {
                RecorderFakeUtils.unsetFakeRecorderWindowLayoutParams(mainLayoutParams, iconLayoutParams, canvasLayoutParams, windowManager, mainLayout, iconLayout, canvasLayout, Floating.this);
            }
        });

        // addKeyTimeText(layoutBypasss);
        // addFpsText(layoutBypasss);


        //    addSwitch2("Line",
        addSwitch2(getEspMenuText("Line", "线"),
                GetBoolean("ESP_LINE"),
                (buttonView, isChecked) -> UpdateConfiguration2("ESP_LINE", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);


        //  addSwitch2("Bone",
        addSwitch2(getEspMenuText("Bone", "骨骼"),
                GetBoolean("ESP_BONE"),
                (buttonView, isChecked) -> UpdateConfiguration2("ESP_BONE", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);


        //addSwitch2("Info",
        addSwitch2(getEspMenuText("Info", "玩家信息"),
                GetBoolean("ESP_INFO"), (buttonView, isChecked) -> UpdateConfiguration2("ESP_INFO", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);


        //addSwitch2("Weapon",
        addSwitch2(getEspMenuText("Weapon", "玩家武器"),
                GetBoolean("ESP_WEAPON"), (buttonView, isChecked) -> UpdateConfiguration2("ESP_WEAPON", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);


        //  addSwitch2("Grenade Warning",
        addSwitch2(getEspMenuText("Grenade Warning", "手雷预警"),
                GetBoolean("ESP_WARNING"), (buttonView, isChecked) -> UpdateConfiguration2("ESP_WARNING", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);


        addSwitch2(getEspMenuText("360° Alert", "360° 警报"),

                GetBoolean("ESP_ALERT"), (buttonView, isChecked) -> UpdateConfiguration2("ESP_ALERT", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);

        addSwitch2(getEspMenuText("Radar MAP", "雷达地图"),

                GetBoolean("ESP_RADAR"), (buttonView, isChecked) -> UpdateConfiguration2("ESP_RADAR", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);

        addSwitch2(getEspMenuText("IgnoreBot-ESP", "忽略机器人"),

                GetBoolean("ESP_IGNOREBOTS"), (buttonView, isChecked) -> UpdateConfiguration2("ESP_IGNOREBOTS", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutEsp);


        AddSeekbar2(getEspMenuText("RadarMAP-Size", "雷达地图大小"), 60, 350, GetInteger("RADAR_SIZE"), new SeekBar.OnSeekBarChangeListener() {
            @Override
            //   public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            public void onProgressChanged(SeekBar seekBar, int i, boolean z) {
                UpdateConfiguration2("RADAR_SIZE", i);
                //  UpdateConfiguration2("RADAR_SIZE", progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutEsp);


        // addSwitch2("AimBot",
        addSwitch2(getEspMenuText("AimBot", "自瞄"),
                GetBoolean("NRG_AIMBOT"), (buttonView, isChecked) -> UpdateConfiguration2("NRG_AIMBOT", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },

                layoutCheat);

        addCombo(getEspMenuText("Aim-Target", "瞄准目标"), new String[]{getEspMenuText("Head", "头"), getEspMenuText("Body", "身体")}, "AIM_TARGET", layoutCheat);
        addCombo(getEspMenuText("Aim-Trigger", "瞄准扳机"), new String[]{getEspMenuText("Shoot", "射击"), getEspMenuText("Scope", "开镜"), getEspMenuText("Both", "两个都")}, "AIM_TRIGGER", layoutCheat);


        addSwitch2(getEspMenuText("IgnoreBot", "瞄准-忽略机器人"),
                //  addSwitch2("IgnoreBot",
                GetBoolean("AIM_IGNOREBOTS"), (buttonView, isChecked) -> UpdateConfiguration2("AIM_IGNOREBOTS", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutCheat);


        addSwitch2(getEspMenuText("Aim-Knocked", "瞄准击倒"),
                //  addSwitch2("Aim-Knocked",
                GetBoolean("AIM_KNOCKED"), (buttonView, isChecked) -> UpdateConfiguration2("AIM_KNOCKED", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutCheat);


        addSwitch2(getEspMenuText("iPad View", "iPad 视图"),

                GetBoolean("MEMORY_WIDEVIEW"), (buttonView, isChecked) -> UpdateConfiguration2("MEMORY_WIDEVIEW", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutCheat);


        addSwitch2(getEspMenuText("VisiCheck", "掩体预判"),

                GetBoolean("AIM_VISCHECK"), (buttonView, isChecked) -> UpdateConfiguration2("AIM_VISCHECK", isChecked ? 1 : 0),


                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutCheat);


        addSwitch2(getEspMenuText("Aim-Recoil", "自动瞄准控制后坐力"),

                GetBoolean("RECOI_LCOMPARISON"), (buttonView, isChecked) -> UpdateConfiguration2("RECOI_LCOMPARISON", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutCheat);


        AddSeekbar2(getEspMenuText("RecoilSize", "自瞄控制后坐力大小"), 0, 5, GetInteger("RECOIL_SIZE") == 0 ? 1 : GetInteger("RECOIL_SIZE"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("RECOIL_SIZE", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutCheat);

        AddSeekbar2(getEspMenuText("Aim-Dist", "瞄准距离"), 0, 180, GetInteger("AIM_DISTANCE") == 0 ? 1 : GetInteger("AIM_DISTANCE"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("AIM_DISTANCE", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutCheat);

        AddSeekbar2(getEspMenuText("Fov-Aim", "视野瞄准"), 50, 350, GetInteger("AIM_SIZE") == 0 ? 1 : GetInteger("AIM_SIZE"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("AIM_SIZE", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutCheat);


        addSwitch2(getEspMenuText("Skin-Enable", "Skin-Enable"),
                // addSwitch2("Skin-Enable",
                GetBoolean("SKIN_ENABLE"), (buttonView, isChecked) -> UpdateConfiguration2("SKIN_ENABLE", Integer.valueOf(isChecked ? 1 : 0)),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                // layoutCheat);
                layoutSet);


        addSwitch2(getEspMenuText("DeadBox (Ingame for open)", "DeadBox"),
                //  addSwitch2("DeadBox (Ingame for open)",
                GetBoolean("SKIN_BOXENABLE"), (buttonView, isChecked) -> UpdateConfiguration2("SKIN_BOXENABLE", isChecked ? 1 : 0),

                view -> {
                    //    SaveConfiguration();
                    SaveConfiguration();
                },
                layoutSet);

        AddSeekbar2("X-suit", 0, 13, GetInteger("SKIN_XSUIT"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_XSUIT", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("Set", 0, 72, GetInteger("SKIN_SET"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_SET", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        AddSeekbar2("Skin-BackPack", 0, 16, GetInteger("SKIN_BACKPACK"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_BACKPACK", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);
        AddSeekbar2("Skin-Helmet", 0, 10, GetInteger("SKIN_HELMET"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_HELMET", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        //   AddText("Skin-Gun", layoutSet);

        AddSeekbar2("M416", 0, 11, GetInteger("SKIN_M416"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_M416", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("AKM", 0, 10, GetInteger("SKIN_AKM"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_AKM", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                //

                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("SCAR-L", 0, 7, GetInteger("SKIN_SCARL"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_SCARL", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);
        AddSeekbar2("M762", 0, 9, GetInteger("SKIN_M762"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_M762", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        AddSeekbar2("M16A4", 0, 5, GetInteger("SKIN_M16A4"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_M16A4", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("GROZAR", 0, 7, GetInteger("SKIN_GROZAR"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_GROZAR", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("AUG", 0, 5, GetInteger("SKIN_AUG"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_AUG", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);
        AddSeekbar2("ACE32", 0, 3, GetInteger("SKIN_ACE32"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_ACE32", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);
        AddSeekbar2("M249", 0, 4, GetInteger("SKIN_M249"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_M249", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);
        AddSeekbar2("DP28", 0, 4, GetInteger("SKIN_DP28"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_DP28", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("MG3", 0, 1, GetInteger("SKIN_MG3"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_MG3", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);
        //  AddText("SMG", layoutSet);

        AddSeekbar2("P90", 0, 1, GetInteger("SKIN_P90"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_P90", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("UZI", 0, 6, GetInteger("SKIN_UZI"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_UZI", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        AddSeekbar2("UMP45", 0, 8, GetInteger("SKIN_UMP45"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_UMP45", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        AddSeekbar2("VECTOR", 0, 4, GetInteger("SKIN_VECTOR"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_VECTOR", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        AddSeekbar2("THOMPSON", 0, 4, GetInteger("SKIN_THOMPSON"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_THOMPSON", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        //  AddText("Sniper", layoutSet);
        AddSeekbar2("M24", 0, 5, GetInteger("SKIN_M24"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_M24", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("KAR98K", 0, 6, GetInteger("SKIN_KAR98K"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_KAR98K", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("AWM", 0, 7, GetInteger("SKIN_AWM"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_AWM", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("AMR", 0, 1, GetInteger("SKIN_AMR"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_AMR", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("MK14", 0, 2, GetInteger("SKIN_MK14"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_MK14", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        //   AddText("Skin-Vehicle", layoutSet);

        AddSeekbar2("Dacia", 0, 23, GetInteger("SKIN_DACIA"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_DACIA", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("CoupeRP", 0, 35, GetInteger("SKIN_COUPERP"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_COUPERP", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        AddSeekbar2("UAZ", 0, 13, GetInteger("SKIN_UAZ"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_UAZ", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);

        AddSeekbar2("Moto", 0, 8, GetInteger("SKIN_MOTO"), new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                UpdateConfiguration2("SKIN_MOTO", progress);

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                SaveConfiguration();
            }
        }, layoutSet);


        mainLayout.addView(radioGroup);

    }


    void AddText(Object data, String text, float size, int color) {
        TextView textView = new TextView(this);
        textView.setText(text);
        textView.setTextColor(color);
        textView.setShadowLayer(5, 5, 5, Color.BLACK);
        textView.setPadding(15, 15, 15, 15);
        textView.setTextSize(convertSizeToDp(size));
        textView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));

        if (data instanceof Integer)
            pageLayouts[(Integer) data].addView(textView);
        else if (data instanceof ViewGroup)
            ((ViewGroup) data).addView(textView);
    }

    int convertSizeToDp(float size) {
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        float fpixels = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, size, metrics);
        return Math.round(fpixels);
    }


    int dp(int i) {
        return dpToPx(i);
    }

    int dpi(float dp) {
        return (int) (dp * this.getResources().getDisplayMetrics().density + 0.5f);
    }

    int convertSizeToDp22() {
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        float fpixels = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, (float) 2.5, metrics);
        return Math.round(fpixels);
    }


    public void addSwitch2(String label, boolean isChecked,
                           CompoundButton.OnCheckedChangeListener listener,
                           View.OnClickListener longClickListener,
                           LinearLayout parent) {
        LinearLayout containerLayout = new LinearLayout(this);
        containerLayout.setOrientation(LinearLayout.HORIZONTAL);
        containerLayout.setGravity(Gravity.CENTER_VERTICAL);
        containerLayout.setPadding(10, 0, 10, 10);


        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                0, // width = 0 with weight will make it take remaining space
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        textParams.weight = 1;

        TextView labelText = new TextView(this);
        labelText.setText(label);
        labelText.setTextSize(13.0f);
        labelText.setTextColor(Color.WHITE);
        labelText.setPadding(20, 0, 20, 10);
        labelText.setLayoutParams(textParams);


        LinearLayout.LayoutParams switchParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        switchParams.gravity = Gravity.END;

        @SuppressLint("UseSwitchCompatOrMaterialCode")
        Switch switchButton = new Switch(this);
        switchButton.setChecked(isChecked);
        switchButton.setLayoutParams(switchParams);

        // Modern switch colors (red for ON, green for OFF)
        ColorStateList thumbStates = new ColorStateList(
                new int[][]{
                        new int[]{android.R.attr.state_checked},
                        new int[]{}
                },
                new int[]{
                        Color.WHITE,
                        Color.GREEN
                }
        );

        ColorStateList trackStates = new ColorStateList(
                new int[][]{
                        new int[]{android.R.attr.state_checked},
                        new int[]{}
                },
                new int[]{
                        Color.parseColor("#00FFFFFF"), // Semi-transparent red
                        Color.parseColor("#40FFFFFF") // Semi-transparent green 4000FF00
                }
        );
        switchButton.setThumbTintList(thumbStates);
        switchButton.setTrackTintList(trackStates);
        switchButton.setOnCheckedChangeListener(listener);
        switchButton.setOnLongClickListener(v -> {
            if (longClickListener != null) {
                longClickListener.onClick(v);
            }
            return true;
        });

        containerLayout.addView(labelText);
        containerLayout.addView(switchButton);
        parent.addView(containerLayout);

        View lineView = new View(this);
        lineView.setBackgroundColor(Color.parseColor("#FFFFFF"));
        LinearLayout.LayoutParams lineParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, 1);
        lineParams.setMargins(20, 5, 20, 10);
        lineView.setLayoutParams(lineParams);
        parent.addView(lineView);
        addSpacing(parent, 5);
    }

    @SuppressLint("SetTextI18n")
    public void addCheckboxWithSwitch(LinearLayout parent, CompoundButton.OnCheckedChangeListener listener) {
        LinearLayout containerLayout = new LinearLayout(this);
        containerLayout.setOrientation(LinearLayout.HORIZONTAL);
        containerLayout.setGravity(Gravity.CENTER_VERTICAL);
        containerLayout.setPadding(10, 0, 10, 10);

        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                0,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        textParams.weight = 1; // Takes all remaining space

        TextView labelText = new TextView(this);
        labelText.setText(getEspMenuText("LiveStreamMode(Recording Hide)", "隐藏模式"));
        labelText.setTextSize(12.0f);
        labelText.setTextColor(Color.WHITE);
        labelText.setPadding(20, 0, 20, 10);
        labelText.setLayoutParams(textParams);

        // Create layout params for the switch
        LinearLayout.LayoutParams switchParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        switchParams.gravity = Gravity.END;

        @SuppressLint("UseSwitchCompatOrMaterialCode")
        Switch switchButton = new Switch(this);
        switchButton.setChecked(GetBoolean("RECORDER_HIDE"));
        switchButton.setLayoutParams(switchParams);

        // Modern material design colors
        int thumbColor = Color.parseColor("#FFFFFF");
        int trackColor = Color.parseColor("#64DD17");

        switchButton.setThumbTintList(ColorStateList.valueOf(thumbColor));
        switchButton.setTrackTintList(ColorStateList.valueOf(trackColor));
        switchButton.setOnCheckedChangeListener(listener);

        containerLayout.addView(labelText);
        containerLayout.addView(switchButton);
        parent.addView(containerLayout);

        View lineView = new View(this);
        lineView.setBackgroundColor(Color.parseColor("#E0E0E0"));
        LinearLayout.LayoutParams lineParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, 1);
        lineParams.setMargins(20, 5, 20, 10);
        lineView.setLayoutParams(lineParams);
        parent.addView(lineView);
        addSpacing(parent, 5);
    }


    // Define the missing AddCheckbox method
    @SuppressLint("SetTextI18n")
    private void AddCheckbox(LinearLayout parent, CompoundButton.OnCheckedChangeListener listener) {
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.HORIZONTAL);
        layout.setGravity(Gravity.CENTER_VERTICAL);
        layout.setPadding(10, 5, 10, 5);

        TextView textView = new TextView(this);
        textView.setText("LiveStreamMode(Recording Hide)");
        textView.setTextSize(10.0f);
        textView.setTextColor(Color.BLACK);
        textView.setPadding(20, 0, 20, 0);

        @SuppressLint("UseSwitchCompatOrMaterialCode") Switch switchBtn = new Switch(this);
        switchBtn.setChecked(false);
        switchBtn.setOnCheckedChangeListener(listener);

        layout.addView(textView);
        layout.addView(switchBtn);
        parent.addView(layout);

        View lineView = new View(this);
        lineView.setBackgroundColor(0xFF000000);
        LinearLayout.LayoutParams lineParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, 1);
        lineParams.setMargins(20, 0, 20, 10);
        lineView.setLayoutParams(lineParams);
        parent.addView(lineView);
        addSpacing(parent, 5);
    }

    @SuppressLint("RtlHardcoded")
    void addRadioButton(Object object, String[] list, int value, RadioGroup.OnCheckedChangeListener listener) {
        RadioGroup rg = new RadioGroup(this);
        RadioButton[] radioButtonArr = new RadioButton[list.length];
        rg.setOrientation(RadioGroup.HORIZONTAL);
        for (int i = 0; i < list.length; i++) {
            radioButtonArr[i] = new RadioButton(this);
            if (i == value) radioButtonArr[i].setChecked(true);
            radioButtonArr[i].setPadding(15, 15, 15, 15);
            radioButtonArr[i].setText(list[i]);

            radioButtonArr[i].setTextSize(convertSizeToDp(mediumSize));
            radioButtonArr[i].setId(i);
            radioButtonArr[i].setGravity(Gravity.RIGHT);
            radioButtonArr[i].setTextColor(Color.WHITE);


            ColorStateList colorStateList = new ColorStateList(
                    new int[][]{
                            new int[]{-android.R.attr.state_checked}, // unchecked
                            new int[]{android.R.attr.state_checked}  // checked
                    },
                    new int[]{
                            Color.BLACK,
                            Color.WHITE
                    }
            );

            radioButtonArr[i].setButtonTintList(colorStateList);
            rg.addView(radioButtonArr[i]);
        }

        rg.setOnCheckedChangeListener(listener);
        RelativeLayout.LayoutParams toggleP = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        rg.setLayoutParams(toggleP);


        if (object instanceof Integer)
            this.pageLayouts[(Integer) object].addView(rg);
        else if (object instanceof ViewGroup)
            ((ViewGroup) object).addView(rg);
    }


    @SuppressLint("SetTextI18n")
    void AddSeekbar2(String text, final int min, int max, int value, final SeekBar.OnSeekBarChangeListener listener, LinearLayout subWindow) {
        LinearLayout containerLayout = new LinearLayout(this);
        containerLayout.setOrientation(LinearLayout.VERTICAL);
        containerLayout.setPadding(dpi(10), dpi(6), dpi(10), dpi(6));

        // Label text with new style
        TextView textV = new TextView(this);
        textV.setText(text);
        textV.setTextSize(14);
        textV.setTextColor(Color.parseColor("#E0E0E0"));
        textV.setTypeface(DEFAULT_BOLD);
        textV.setPadding(dpi(4), 0, dpi(4), dpi(6));

        // Seekbar row
        LinearLayout seekbarRow = new LinearLayout(this);
        seekbarRow.setOrientation(LinearLayout.HORIZONTAL);
        seekbarRow.setGravity(Gravity.CENTER_VERTICAL);
        seekbarRow.setPadding(dpi(4), 0, dpi(4), 0);

        // Value display with new color
        final TextView textValue = new TextView(this);
        textValue.setText(String.valueOf(value > 0 ? value : min));
        textValue.setTextSize(13);
        textValue.setTextColor(Color.parseColor("#64B5F6")); // New blue accent
        textValue.setTypeface(DEFAULT_BOLD);
        textValue.setGravity(Gravity.CENTER);
        textValue.setMinWidth(dpi(35));

        // Modern seekbar track
        GradientDrawable trackBg = new GradientDrawable();
        trackBg.setColor(Color.parseColor("#1E1E1E")); // Darker background
        trackBg.setCornerRadius(dpi(5));

        // Seekbar with updated colors
        SeekBar seekBar = new SeekBar(this);
        seekBar.setMax(max);
        seekBar.setMin(min);
        seekBar.setProgress(value > 0 ? value : min);
        seekBar.setProgressTintList(ColorStateList.valueOf(Color.parseColor("#64B5F6"))); // New blue accent
        seekBar.setProgressBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#FF5722"))); // Darker track
        seekBar.setBackground(trackBg);

        // Modern thumb design
        GradientDrawable thumbDrawable = new GradientDrawable();
        thumbDrawable.setShape(GradientDrawable.OVAL);
        thumbDrawable.setColor(Color.parseColor("#64B5F6")); // New blue accent
        thumbDrawable.setSize(dpi(14), dpi(14)); // Slightly smaller thumb
        seekBar.setThumb(thumbDrawable);

        // Layout parameters
        LinearLayout.LayoutParams seekBarParams = new LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1f);
        seekBarParams.setMargins(dpi(4), 0, dpi(4), 0);
        seekBar.setLayoutParams(seekBarParams);

        // Enhanced seekbar listener
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (progress < min) {
                    progress = min;
                    seekBar.setProgress(progress);
                }
                if (listener != null) {
                    listener.onProgressChanged(seekBar, progress, fromUser);
                }
                textValue.setText(String.valueOf(progress));

                // Smoother animation
                textValue.animate()
                        .scaleX(1.1f)
                        .scaleY(1.1f)
                        .setDuration(150)
                        .setInterpolator(new android.view.animation.DecelerateInterpolator())
                        .withEndAction(() -> textValue.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .setDuration(150)
                                .setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator())
                                .start());
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                if (listener != null) {
                    listener.onStartTrackingTouch(seekBar);
                }
                thumbDrawable.setColor(Color.parseColor("#90CAF9")); // Lighter blue when pressed
                seekBar.setThumb(thumbDrawable);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (listener != null) {
                    listener.onStopTrackingTouch(seekBar);
                }
                thumbDrawable.setColor(Color.parseColor("#64B5F6")); // Return to normal blue
                seekBar.setThumb(thumbDrawable);
                SaveConfiguration();
            }
        });

        // Refined container background
        GradientDrawable containerBg = new GradientDrawable();
        containerBg.setColor(Color.parseColor("#202020")); // Darker background
        containerBg.setCornerRadius(dpi(8));
        containerBg.setStroke(dpi(1), Color.parseColor("#2C2C2C")); // Subtle border
        containerLayout.setBackground(containerBg);

        // Add views
        seekbarRow.addView(seekBar);
        seekbarRow.addView(textValue);
        containerLayout.addView(textV);
        containerLayout.addView(seekbarRow);

        // Compact margins
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        containerParams.setMargins(dpi(6), dpi(3), dpi(6), dpi(3));
        containerLayout.setLayoutParams(containerParams);

        subWindow.addView(containerLayout);
        addSpacing(subWindow, 3);
    }

    private int convertSizeToDp(int size) {
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        float fpixels = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, size, metrics);
        return Math.round(fpixels);
    }

    private void addCombo(String label, String[] options, final String configKey, LinearLayout subWindow) {
        // Create container with subtle elevation
        RelativeLayout containerLayout = new RelativeLayout(this);
        containerLayout.setBackground(selectedBackground());
        containerLayout.setPadding(dpToPx(12), dpToPx(8), dpToPx(12), dpToPx(8));

        // Horizontal layout with reduced size
        LinearLayout rowLayout = new LinearLayout(this);
        rowLayout.setOrientation(LinearLayout.HORIZONTAL);
        rowLayout.setGravity(Gravity.CENTER_VERTICAL | Gravity.END); // Align to end
        rowLayout.setLayoutParams(new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        // Smaller label text
        TextView labelText = new TextView(this);
        labelText.setText(label);
        labelText.setTextSize(13);
        labelText.setTypeface(DEFAULT_BOLD);
        labelText.setTextColor(Color.parseColor("#383838"));
        labelText.setPadding(dpToPx(2), dpToPx(2), dpToPx(8), dpToPx(2));
        labelText.setGravity(Gravity.START | Gravity.CENTER_VERTICAL);

        // Label takes left portion
        LinearLayout.LayoutParams labelParams = new LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1.0f);
        labelText.setLayoutParams(labelParams);

        // Options scroll view aligned to end
        HorizontalScrollView scrollView = new HorizontalScrollView(this);
        scrollView.setHorizontalScrollBarEnabled(false);

        LinearLayout optionsLayout = new LinearLayout(this);
        optionsLayout.setOrientation(LinearLayout.HORIZONTAL);
        optionsLayout.setGravity(Gravity.END | Gravity.CENTER_VERTICAL);
        optionsLayout.setPadding(dpToPx(2), dpToPx(2), dpToPx(2), dpToPx(2));

        String selectedOption = configMap.get(configKey);
        int selectedIndex = selectedOption != null ? Integer.parseInt(selectedOption) : 0;
        List<TextView> optionViews = new ArrayList<>();


        for (int i = 0; i < options.length; i++) {
            TextView optionView = new TextView(this);
            optionView.setText(options[i]);
            optionView.setTextSize(12);
            optionView.setTypeface(DEFAULT);
            optionView.setPadding(dpToPx(12), dpToPx(6), dpToPx(12), dpToPx(6));
            optionView.setGravity(Gravity.CENTER);

            GradientDrawable normalBg = new GradientDrawable();
            normalBg.setColor(Color.parseColor("#383838"));
            normalBg.setCornerRadius(dpToPx(15));

            GradientDrawable selectedBg = new GradientDrawable();
            selectedBg.setColor(Color.parseColor("#4CAF50"));
            selectedBg.setCornerRadius(dpToPx(15));

            final int index = i;
            optionView.setOnClickListener(v -> {
                for (TextView view : optionViews) {
                    view.setBackground(normalBg);
                    view.setTextColor(Color.parseColor("#FFFFFF"));
                }
                optionView.setBackground(selectedBg);
                optionView.setTextColor(Color.parseColor("#FFFFFF"));
                UpdateConfiguration2(configKey, index);
            });

            optionView.setBackground(i == selectedIndex ? selectedBg : normalBg);
            optionView.setTextColor(Color.parseColor("#FFFFFF"));

            if (i > 0) {
                Space space = new Space(this);
                space.setLayoutParams(new LinearLayout.LayoutParams(dpToPx(4), 0));
                optionsLayout.addView(space);
            }

            optionsLayout.addView(optionView);
            optionViews.add(optionView);
        }

        scrollView.addView(optionsLayout);
        rowLayout.addView(labelText);
        rowLayout.addView(scrollView);
        containerLayout.addView(rowLayout);

        // Reduced margins
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        containerParams.setMargins(dpToPx(4), dpToPx(4), dpToPx(4), dpToPx(4));
        containerLayout.setLayoutParams(containerParams);

        subWindow.addView(containerLayout);
    }


    private GradientDrawable selectedBackground() {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setColor(Color.WHITE);
        drawable.setCornerRadius(10);
        return drawable;
    }

    private int dpToPxLine() {
        return Math.round((float) 0.5 * getResources().getDisplayMetrics().density);
    }


    public int dpToPx(int i) {
        return Math.round(((float) i) * getResources().getDisplayMetrics().density);
    }



    void addRadioButtonGroup(String label, String option1, String option2, String option3, /*boolean isOption1Checked, boolean isOption2Checked,*/
                             final View.OnClickListener onClickOption1, final View.OnClickListener onClickOption2, final View.OnClickListener onClickOption3,
                             LinearLayout subWindow) {
        LinearLayout containerLayout = new LinearLayout(this);
        containerLayout.setOrientation(LinearLayout.HORIZONTAL);
        containerLayout.setGravity(Gravity.CENTER_VERTICAL);
        containerLayout.setPadding(10, 0, 10, 10);
        TextView labelText = new TextView(this);
        labelText.setText(label);
        labelText.setTextSize(10.0f);
        labelText.setTextColor(Color.BLACK);
        labelText.setPadding(20, 0, 20, 10);
        containerLayout.addView(labelText);
        RelativeLayout backgroundLayout = getRelativeLayout();
        final TextView radioButton1 = new TextView(this);
        final TextView radioButton2 = new TextView(this);
        final TextView radioButton3 = new TextView(this);
        radioButton1.setText(option1);
        radioButton1.setTextSize(10.0f);
        radioButton1.measure(0, 0);
        radioButton1.setPadding(20, 12, 20, 10);
        radioButton1.setGravity(Gravity.CENTER);
        radioButton1.setTextColor(Color.BLACK);
        radioButton1.setBackground(null);

        if (onClickOption1 != null) {
            radioButton1.setTextColor(Color.BLACK);
            radioButton2.setTextColor(Color.BLACK);
            radioButton3.setTextColor(Color.BLACK);
            radioButton1.setBackground(createWhiteBackground());
            radioButton2.setBackground(null);
            radioButton3.setBackground(null);
        }


        radioButton1.setOnClickListener(v -> {
            radioButton1.setTextColor(Color.BLACK);
            radioButton2.setTextColor(Color.BLACK);
            radioButton3.setTextColor(Color.BLACK);
            radioButton1.setBackground(createWhiteBackground());
            radioButton2.setBackground(null);
            radioButton3.setBackground(null);
            if (onClickOption1 != null) {
                onClickOption1.onClick(v);
            }
        });
        radioButton2.setText(option2);
        radioButton2.setTextSize(10.0f);
        radioButton2.measure(0, 0);
        radioButton2.setPadding(20, 12, 20, 10);
        radioButton2.setGravity(Gravity.CENTER);
        radioButton2.setTextColor(Color.BLACK);
        radioButton2.setBackground(null);
        radioButton2.setOnClickListener(v -> {
            radioButton1.setTextColor(Color.BLACK);
            radioButton2.setTextColor(Color.BLACK);
            radioButton3.setTextColor(Color.BLACK);
            radioButton2.setBackground(createWhiteBackground());
            radioButton1.setBackground(null);
            radioButton3.setBackground(null);
            if (onClickOption2 != null) {
                onClickOption2.onClick(v);
            }
        });
        radioButton3.setText(option3);
        radioButton3.setTextSize(10.0f);
        radioButton3.measure(0, 0);
        radioButton3.setPadding(20, 12, 20, 10);
        radioButton3.setGravity(Gravity.CENTER);
        radioButton3.setTextColor(Color.BLACK);
        radioButton3.setBackground(null);
        radioButton3.setOnClickListener(v -> {
            radioButton1.setTextColor(Color.BLACK);
            radioButton2.setTextColor(Color.BLACK);
            radioButton3.setTextColor(Color.BLACK);
            radioButton3.setBackground(createWhiteBackground());
            radioButton1.setBackground(null);
            radioButton2.setBackground(null);
            if (onClickOption3 != null) {
                onClickOption3.onClick(v);
            }
        });
        RelativeLayout.LayoutParams radioButtonParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        radioButtonParams.addRule(RelativeLayout.CENTER_VERTICAL);
        radioButtonParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        radioButtonParams.setMargins(0, 10, 10, 5);
        radioButton1.setLayoutParams(radioButtonParams);
        radioButton2.setLayoutParams(radioButtonParams);
        radioButton3.setLayoutParams(radioButtonParams);
        LinearLayout radioGroupLayout = new LinearLayout(this);
        radioGroupLayout.setOrientation(LinearLayout.HORIZONTAL);
        radioGroupLayout.setGravity(Gravity.CENTER);
        radioGroupLayout.setPadding(10, 0, 10, 10);
        radioGroupLayout.addView(radioButton1);
        radioGroupLayout.addView(radioButton2);
        radioGroupLayout.addView(radioButton3);
        backgroundLayout.addView(radioGroupLayout);
        containerLayout.addView(backgroundLayout);
        subWindow.addView(containerLayout);
        View lineView = new View(this);
        lineView.setBackgroundColor(0xFF000000);
        LinearLayout.LayoutParams lineParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1);
        lineParams.setMargins(20, 0, 20, 10);
        lineView.setLayoutParams(lineParams);
        subWindow.addView(lineView);
        addSpacing(subWindow, 5);
    }

    @NonNull
    private RelativeLayout getRelativeLayout() {
        RelativeLayout backgroundLayout = new RelativeLayout(this);
        GradientDrawable backgroundDrawable = new GradientDrawable();
        backgroundDrawable.setColor(0xFFEEEEEE);
        backgroundDrawable.setCornerRadius(15);
        backgroundLayout.setBackground(backgroundDrawable);
        RelativeLayout.LayoutParams backgroundLayoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);


        backgroundLayoutParams.addRule(RelativeLayout.ALIGN_PARENT_END);
        backgroundLayoutParams.addRule(RelativeLayout.CENTER_VERTICAL);
        backgroundLayoutParams.setMargins(160, 10, 10, 3);
        backgroundLayout.setLayoutParams(backgroundLayoutParams);
        return backgroundLayout;
    }


    private Drawable createWhiteBackground() {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setColor(Color.WHITE);
        drawable.setCornerRadius(10);
        return drawable;
    }

    void addLogoText(String text) {
        TextView textView = new TextView(this);
        textView.setText(text);
        textView.setTextColor(0xFFFFFFFF);


        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
        textView.setGravity(Gravity.CENTER);

        textView.setOnClickListener(v -> {
            mainLayout.setVisibility(View.GONE);
            iconLayout.setVisibility(View.VISIBLE);
        });
        textView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        bodyLayout.addView(textView);
    }

    void addSpacing(ViewGroup parentLayout, int i) {
        View spaceView = new View(parentLayout.getContext());
        spaceView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 5));
        parentLayout.addView(spaceView);
    }


    @SuppressLint("ClickableViewAccessibility")
    void CreateIcon() {
        iconLayout = new RelativeLayout(this);
        RelativeLayout.LayoutParams iconParams = new RelativeLayout.LayoutParams(
                dpToPx(48),  // Fixed width
                dpToPx(48)   // Fixed height
        );
        iconLayout.setLayoutParams(iconParams);

        iconImg = new ImageView(this);
        ViewGroup.LayoutParams iconImgParams = new ViewGroup.LayoutParams(
                dpToPx(48),  // Fixed width
                dpToPx(48)   // Fixed height
        );
        iconImg.setLayoutParams(iconImgParams);
        iconImg.setScaleType(ImageView.ScaleType.FIT_CENTER);  // Ensure icon fits properly
        GetBoolean("RECORDER_HIDE");
        iconLayout.addView(iconImg);

        try {
            String iconBase64 = iconenc();
            byte[] iconData = Base64.decode(iconBase64, Base64.DEFAULT);
            Bitmap bmp = BitmapFactory.decodeByteArray(iconData, 0, iconData.length);
            iconImg.setImageBitmap(bmp);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        iconLayoutParams = new WindowManager.LayoutParams();
        iconLayoutParams.width = iconSize;
        iconLayoutParams.height = iconSize;
        iconLayoutParams.type = type;
        iconLayoutParams.format = PixelFormat.RGBA_8888;
        iconLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;

        iconLayoutParams.gravity = Gravity.START | Gravity.TOP;
        iconLayoutParams.x = 0;
        iconLayoutParams.y = 0;
        iconLayoutParams.layoutInDisplayCutoutMode = LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        iconLayout.setVisibility(View.VISIBLE);
        iconLayout.setOnTouchListener(new View.OnTouchListener() {
            int lastX;
            int lastY;
            float pressedX;
            float pressedY;
            float deltaX;
            float deltaY;
            float newX;
            float newY;
            float maxX;
            float maxY;
            @Override
            public boolean onTouch(View v, MotionEvent event) {

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        lastX = iconLayoutParams.x;
                        lastY = iconLayoutParams.y;
                        deltaX = lastX - event.getRawX();
                        deltaY = lastY - event.getRawY();

                        pressedX = event.getRawX();
                        pressedY = event.getRawY();

                        // Use vibrator API with proper API level checks
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                            // API 31+ - Use VibratorManager
                            VibratorManager vibratorManager = (VibratorManager) getSystemService(Context.VIBRATOR_MANAGER_SERVICE);
                            if (vibratorManager != null) {
                                VibrationEffect vibrationEffect = VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE);
                                vibratorManager.getDefaultVibrator().vibrate(vibrationEffect);
                            }
                        } else {
                            // API 30 and below - Use legacy Vibrator
                            performLegacyVibration();
                        }

                        break;
                    case MotionEvent.ACTION_UP:
                        int Xdiff = (int) (event.getRawX() - pressedX);
                        int Ydiff = (int) (event.getRawY() - pressedY);

                        if (Xdiff == 0 && Ydiff == 0) {
                            mainLayout.setVisibility(View.VISIBLE);
                            iconLayout.setVisibility(View.GONE);
                        }
                        return false;

                    case MotionEvent.ACTION_MOVE:
                        newX = event.getRawX() + deltaX;
                        newY = event.getRawY() + deltaY;

                        float maxX = screenWidth - v.getWidth();
                        float maxY = screenHeight - v.getHeight();

                        if (newX < 0)
                            newX = 0;
                        if (newX > maxX)
                            newX = (int) maxX;
                        if (newY < 0)
                            newY = 0;
                        if (newY > maxY)
                            newY = (int) maxY;

                        iconLayoutParams.x = (int) newX;
                        iconLayoutParams.y = (int) newY;

                        mainLayoutParams.x = iconLayoutParams.x;
                        mainLayoutParams.y = iconLayoutParams.y;
                        windowManager.updateViewLayout(mainLayout, mainLayoutParams);
                        windowManager.updateViewLayout(iconLayout, iconLayoutParams);
                        break;

                    default:
                        break;
                }

                return false;
            }
        });

        windowManager.addView(iconLayout, iconLayoutParams);
    }

    LinearLayout CreateHolder(Object data) {
        RelativeLayout parentHolder = new RelativeLayout(this);
        parentHolder.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        LinearLayout childHolder = new LinearLayout(this);
        childHolder.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        childHolder.setOrientation(LinearLayout.HORIZONTAL);
        parentHolder.addView(childHolder);

        if (data instanceof Integer)
            pageLayouts[(Integer) data].addView(parentHolder);
        else if (data instanceof ViewGroup)
            ((ViewGroup) data).addView(parentHolder);

        return childHolder;
    }


    /**
     * Thread-safe handler for UI updates
     * Fixed recursive call issue and improved error handling
     */
    private final Handler uiUpdateHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (!isServiceRunning.get()) {
                Log.d(TAG, "Service not running, ignoring UI update");
                return;
            }

            try {
                updateDisplayMetrics();
                updateLayoutDimensions();
                Log.d(TAG, "UI layout updated successfully");

            } catch (Exception e) {
                Log.e(TAG, "Error updating UI layout", e);
            }
        }
    };

    /**
     * Update display metrics safely
     */
    private void updateDisplayMetrics() {
        try {
            if (windowManager == null) return;

            // Use the new utility method for screen size
            Point screenSize = getScreenSize();
            screenWidth = screenSize.x;
            screenHeight = screenSize.y;

            Log.d(TAG, "Display metrics updated: " + screenWidth + "x" + screenHeight);

        } catch (Exception e) {
            Log.e(TAG, "Error updating display metrics", e);
        }
    }

    /**
     * Update layout dimensions safely
     */
    private void updateLayoutDimensions() {
        try {
            if (windowManager == null) return;

            // Update main layout
            if (mainLayout != null && mainLayoutParams != null) {
                mainLayoutParams.width = layoutWidth;
                mainLayoutParams.height = layoutHeight;
                windowManager.updateViewLayout(mainLayout, mainLayoutParams);
            }

            // Update canvas layout
            if (canvasLayout != null && canvasLayoutParams != null) {
                canvasLayoutParams.width = screenWidth;
                canvasLayoutParams.height = screenHeight;
                windowManager.updateViewLayout(canvasLayout, canvasLayoutParams);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error updating layout dimensions", e);
        }
    }

    public int GetDeviceMaxFps() {
        // Use the new utility method for display metrics
        DisplayMetrics metrics = getDisplayMetrics();
        float refreshRate = 60.0f; // Default fallback

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            @SuppressWarnings("deprecation")
            Display display = windowManager.getDefaultDisplay();
            refreshRate = display.getRefreshRate();
        }
        return Math.round(refreshRate);
    }

    /**
     * Optimized canvas update thread with better performance and error handling
     */
    private final Thread mUpdateCanvas = new Thread("BearMod-Canvas") {
        @Override
        public void run() {
            Log.d(TAG, "Canvas update thread started");
            Process.setThreadPriority(Process.THREAD_PRIORITY_DISPLAY);

            long frameTime = 1000 / Math.max(GetDeviceMaxFps(), 30); // Minimum 30 FPS

            while (isServiceRunning.get() && !isInterrupted()) {
                try {
                    long startTime = System.currentTimeMillis();

                    // Update canvas only if it exists and service is running
                    if (canvasLayout != null && isServiceRunning.get()) {
                        canvasLayout.postInvalidate();
                    }

                    // Calculate sleep time for consistent frame rate
                    long processingTime = System.currentTimeMillis() - startTime;
                    long sleepTime = Math.max(1, frameTime - processingTime);

                    Thread.sleep(sleepTime);

                } catch (InterruptedException e) {
                    Log.d(TAG, "Canvas thread interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error in canvas update thread", e);
                    // Continue running unless it's a critical error
                    try {
                        Thread.sleep(100); // Brief pause before retry
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            Log.d(TAG, "Canvas update thread stopped");
        }
    };

    /**
     * Optimized display monitoring thread with better performance and error handling
     */
    private final Thread mUpdateThread = new Thread("BearMod-Display") {
        @Override
        public void run() {
            Log.d(TAG, "Display update thread started");
            Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);

            long checkInterval = 500; // Check every 500ms instead of every frame

            while (isServiceRunning.get() && !isInterrupted()) {
                try {
                    long startTime = System.currentTimeMillis();

                    // Check display changes only if service is running
                    if (windowManager != null && isServiceRunning.get()) {
                        // Use the new utility method for screen size
                        Point screenSize = getScreenSize();

                        // Only update if screen size actually changed
                        if (screenWidth != screenSize.x || screenHeight != screenSize.y) {
                            Log.d(TAG, "Screen size changed: " + screenSize.x + "x" + screenSize.y);
                            uiUpdateHandler.sendEmptyMessage(0);
                        }
                    }

                    // Calculate sleep time
                    long processingTime = System.currentTimeMillis() - startTime;
                    long sleepTime = Math.max(1, checkInterval - processingTime);

                    Thread.sleep(sleepTime);

                } catch (InterruptedException e) {
                    Log.d(TAG, "Display thread interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error in display update thread", e);
                    // Continue running unless it's a critical error
                    try {
                        Thread.sleep(1000); // Longer pause for display errors
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            Log.d(TAG, "Display update thread stopped");
        }
    };

    // ========== Performance Testing Support Methods ==========

    /**
     * Check if service is running (for performance tests)
     */
    public boolean isServiceRunning() {
        return isServiceRunning.get();
    }

    /**
     * Check if service is initialized (for performance tests)
     */
    public boolean isInitialized() {
        return isInitialized.get();
    }

    /**
     * Get configuration map size (for performance tests)
     */
    public int getConfigMapSize() {
        return configMap.size();
    }

    /**
     * Clear test configurations (for performance tests)
     */
    public void clearTestConfigurations() {
        configMap.entrySet().removeIf(entry ->
            entry.getKey().startsWith("TEST_") ||
            entry.getKey().startsWith("CLEANUP_TEST_") ||
            entry.getKey().startsWith("THREAD_"));
    }



}
