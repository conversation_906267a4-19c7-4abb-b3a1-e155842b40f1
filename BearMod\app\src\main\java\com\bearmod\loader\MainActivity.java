package com.bearmod.loader;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;
import android.widget.TextView;
import android.widget.LinearLayout;

public class MainActivity extends Activity {

    private static final String TAG = "BearMod_MainActivity";
    private static boolean nativeLibraryLoaded = false;

    static {
        try {
            System.loadLibrary("bearmod");
            nativeLibraryLoaded = true;
            Log.d(TAG, "Native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library: " + e.getMessage());
            nativeLibraryLoaded = false;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "MainActivity onCreate started");

        // Create a simple UI to show we're running
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);

        TextView statusText = new TextView(this);
        statusText.setText("BearMod Loading...");
        statusText.setTextSize(20);
        layout.addView(statusText);

        setContentView(layout);

        try {
            if (!nativeLibraryLoaded) {
                statusText.setText("Error: Native library failed to load");
                Toast.makeText(this, "Native library failed to load", Toast.LENGTH_LONG).show();
                return;
            }

            // Test basic functionality step by step
            Log.d(TAG, "Testing LanguageManager");
            statusText.setText("Initializing LanguageManager...");

            try {
                LanguageManager.getInstance().initialize(this);
                Log.d(TAG, "LanguageManager initialized successfully");
                statusText.setText("LanguageManager OK. Starting Launcher...");
            } catch (Exception e) {
                Log.e(TAG, "LanguageManager failed: " + e.getMessage(), e);
                statusText.setText("LanguageManager failed: " + e.getMessage());
                // Continue anyway
            }

            Log.d(TAG, "Testing Launcher.Init");
            try {
                Launcher.Init(this);
                Log.d(TAG, "Launcher.Init completed successfully");
                statusText.setText("BearMod Started Successfully!");
            } catch (Exception e) {
                Log.e(TAG, "Launcher.Init failed: " + e.getMessage(), e);
                statusText.setText("Launcher failed: " + e.getMessage());

                // Try to start Floating service directly
                try {
                    Intent i = new Intent(this, Floating.class);
                    startService(i);
                    statusText.setText("Floating service started directly");
                } catch (Exception e2) {
                    Log.e(TAG, "Failed to start Floating service: " + e2.getMessage(), e2);
                    statusText.setText("All initialization failed");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Critical error in onCreate: " + e.getMessage(), e);
            statusText.setText("Critical error: " + e.getMessage());
            Toast.makeText(this, "Critical error: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
}
