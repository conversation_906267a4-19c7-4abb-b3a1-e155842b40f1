package com.bearmod.loader;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

public class MainActivity extends Activity {

    private static final String TAG = "BearMod_MainActivity";

    static {
        try {
            System.loadLibrary("bearmod");
            Log.d(TAG, "Native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library: " + e.getMessage());
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "MainActivity onCreate started");

        try {
            // Initialize optimized LanguageManager early
            Log.d(TAG, "Initializing LanguageManager");
            LanguageManager.getInstance().initialize(this);
            Log.d(TAG, "LanguageManager initialized successfully");

            Log.d(TAG, "Calling Launcher.Init");
            Launcher.Init(this);
            Log.d(TAG, "Launcher.Init completed successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate: " + e.getMessage(), e);
            Toast.makeText(this, "Error: " + e.getMessage(), Toast.LENGTH_LONG).show();

            // Try to continue without crashing
            try {
                Intent i = new Intent(this, Floating.class);
                startService(i);
            } catch (Exception e2) {
                Log.e(TAG, "Failed to start Floating service: " + e2.getMessage(), e2);
            }
        }
    }
}
