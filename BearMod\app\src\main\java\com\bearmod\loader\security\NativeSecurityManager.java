package com.bearmod.loader.security;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Build;
import android.util.Log;

import java.security.MessageDigest;

/**
 * Fallback Native Security Manager
 * Provides basic security functionality when native components are not available
 * This is a temporary implementation for compilation compatibility
 */
public class NativeSecurityManager {
    
    private static final String TAG = "NativeSecurityManager";
    private static boolean isLibraryLoaded = false;
    
    static {
        try {
            // Try to load native library
            System.loadLibrary("bearmod");
            isLibraryLoaded = true;
            Log.d(TAG, "Native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.w(TAG, "Native library not available, using fallback implementation");
            isLibraryLoaded = false;
        }
    }
    
    /**
     * Verify loader app signature
     * @param context Application context
     * @return true if signature is valid, false otherwise
     */
    public static boolean verifyLoaderSignature(Context context) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, using fallback verification");
            return verifyAppSignatureFallback(context, context.getPackageName());
        }
        
        try {
            return nativeVerifyLoaderSignature(context);
        } catch (Exception e) {
            Log.e(TAG, "Native loader signature verification failed", e);
            // Fallback to Java implementation
            return verifyAppSignatureFallback(context, context.getPackageName());
        }
    }
    
    /**
     * Get signature hash for debugging purposes
     * @param context Application context
     * @param packageName Package name to get signature for
     * @return Signature hash or null if failed
     */
    public static String getSignatureHash(Context context, String packageName) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, using fallback implementation");
            return getAppSignatureFallback(context, packageName);
        }
        
        try {
            return nativeGetSignatureHash(context, packageName);
        } catch (Exception e) {
            Log.e(TAG, "Native signature hash retrieval failed", e);
            return getAppSignatureFallback(context, packageName);
        }
    }
    
    /**
     * Fallback signature verification using Java
     */
    private static boolean verifyAppSignatureFallback(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo;

            {
                // Android 9+ (API 28+) - Use GET_SIGNING_CERTIFICATES
                packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNING_CERTIFICATES);

                if (packageInfo.signingInfo == null) {
                    Log.e(TAG, "No signing info found for package: " + packageName);
                    return false;
                }

                Signature[] signatures;
                if (packageInfo.signingInfo.hasMultipleSigners()) {
                    signatures = packageInfo.signingInfo.getApkContentsSigners();
                } else {
                    signatures = packageInfo.signingInfo.getSigningCertificateHistory();
                }

                if (signatures == null || signatures.length == 0) {
                    Log.e(TAG, "No signatures found for package: " + packageName);
                    return false;
                }

                Signature signature = signatures[0];
                String signatureHash = getSignatureHashFromSignature(signature);
                Log.d(TAG, "App signature hash (modern): " + signatureHash);

                return signatureHash != null && !signatureHash.isEmpty();

            }

        } catch (Exception e) {
            Log.e(TAG, "Fallback signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Fallback signature hash retrieval using Java
     */
    private static String getAppSignatureFallback(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo;

            // Android 9+ (API 28+) - Use GET_SIGNING_CERTIFICATES
            packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNING_CERTIFICATES);

            if (packageInfo.signingInfo == null) {
                Log.e(TAG, "No signing info found for package: " + packageName);
                return null;
            }

            Signature[] signatures;
            if (packageInfo.signingInfo.hasMultipleSigners()) {
                signatures = packageInfo.signingInfo.getApkContentsSigners();
            } else {
                signatures = packageInfo.signingInfo.getSigningCertificateHistory();
            }

            if (signatures == null || signatures.length == 0) {
                Log.e(TAG, "No signatures found for package: " + packageName);
                return null;
            }

            return getSignatureHashFromSignature(signatures[0]);

        } catch (Exception e) {
            Log.e(TAG, "Fallback signature hash retrieval failed", e);
            return null;
        }
    }
    
    /**
     * Generate hash from signature
     */
    private static String getSignatureHashFromSignature(Signature signature) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] digest = md.digest(signature.toByteArray());
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (Exception e) {
            Log.e(TAG, "Error generating signature hash", e);
            return null;
        }
    }
    
    // Native method declarations (will only be called if library is loaded)
    private static native boolean nativeVerifyLoaderSignature(Context context);
    private static native String nativeGetSignatureHash(Context context, String packageName);
}
