package com.bearmod.loader.target;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.util.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Utility class to verify the application signature
 */
public class SignatureVerifier {
    private static final String TAG = "SignatureVerifier";

    /**
     * Verifies if the application signature is valid
     * 
     * @param context Application context
     * @return true if signature is valid, false otherwise
     */
    public static boolean isSignatureValid(Context context) {
        try {
            // Get package info with signatures
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            // Check if signatures exist
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                Log.e(TAG, "No signatures found");
                return false;
            }
            
            // Get the first signature
            Signature signature = packageInfo.signatures[0];
            
            // Get signature hash
            String signatureHash = getSignatureHash(signature);
            Log.d(TAG, "Signature hash: " + signatureHash);
            
            // In a real app, you would compare this hash with a hardcoded expected hash
            // For this example, we'll just check if the signature exists
            return signature != null && signature.toByteArray().length > 0;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found", e);
            return false;
        }
    }
    
    /**
     * Gets the SHA-256 hash of the signature
     * 
     * @param signature Application signature
     * @return SHA-256 hash of the signature
     */
    public static String getSignatureHash(Signature signature) {
        try {
            // Get signature bytes
            byte[] signatureBytes = signature.toByteArray();
            
            // Create SHA-256 digest
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] digest = md.digest(signatureBytes);
            
            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "SHA-256 algorithm not found", e);
            return "";
        }
    }
    
    /**
     * Gets the signature as a hex string
     * 
     * @param context Application context
     * @return Signature as hex string
     */
    public static String getSignatureHex(Context context) {
        try {
            // Get package info with signatures
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            // Check if signatures exist
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                return "No signatures found";
            }
            
Y
            Signature signature = packageInfo.signatures[0];
            byte[] signatureBytes = signature.toByteArray();
            
            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : signatureBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found", e);
            return "Error: " + e.getMessage();
        }
    }
}
