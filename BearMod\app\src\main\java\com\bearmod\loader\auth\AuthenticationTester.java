package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Comprehensive Authentication Testing Utility
 * Tests all aspects of KeyAuth integration and BearMod-Loader compatibility
 */
public class AuthenticationTester {
    
    private static final String TAG = "AuthTester";
    
    /**
     * Test result callback interface
     */
    public interface TestCallback {
        void onTestComplete(TestResult result);
        void onTestProgress(String message);
    }
    
    /**
     * Test result class
     */
    public static class TestResult {
        public final boolean success;
        public final String summary;
        public final String detailedLog;
        public final long testDuration;
        
        public TestResult(boolean success, String summary, String detailedLog, long testDuration) {
            this.success = success;
            this.summary = summary;
            this.detailedLog = detailedLog;
            this.testDuration = testDuration;
        }
    }
    
    /**
     * Run comprehensive authentication test
     * @param context Application context
     * @param licenseKey License key to test with
     * @param callback Test result callback
     */
    public static void runComprehensiveTest(Context context, String licenseKey, TestCallback callback) {
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            StringBuilder detailedLog = new StringBuilder();
            boolean overallSuccess = true;
            
            try {
                // Test 1: Configuration Validation
                updateProgress(callback, "Testing configuration validation...");
                boolean configValid = testConfiguration(detailedLog);
                if (!configValid) overallSuccess = false;
                
                // Test 2: HWID Generation
                updateProgress(callback, "Testing HWID generation...");
                boolean hwidValid = testHWIDGeneration(context, detailedLog);
                if (!hwidValid) overallSuccess = false;
                
                // Test 3: Authentication Manager Initialization
                updateProgress(callback, "Testing authentication manager initialization...");
                boolean initValid = testAuthManagerInit(context, detailedLog);
                if (!initValid) overallSuccess = false;
                
                // Test 4: KeyAuth Authentication
                updateProgress(callback, "Testing KeyAuth authentication...");
                boolean authValid = testKeyAuthAuthentication(context, licenseKey, detailedLog);
                if (!authValid) overallSuccess = false;
                
                // Test 5: Token Generation and Storage
                updateProgress(callback, "Testing token generation and storage...");
                boolean tokenValid = testTokenGeneration(context, detailedLog);
                if (!tokenValid) overallSuccess = false;
                
                // Test 6: BearMod-Loader Compatibility
                updateProgress(callback, "Testing BearMod-Loader compatibility...");
                boolean compatValid = testBearModLoaderCompatibility(context, detailedLog);
                if (!compatValid) overallSuccess = false;
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                String summary = String.format("Authentication Test %s in %dms", 
                    overallSuccess ? "PASSED" : "FAILED", duration);
                
                TestResult result = new TestResult(overallSuccess, summary, detailedLog.toString(), duration);
                
                if (callback != null) {
                    new Handler(Looper.getMainLooper()).post(() -> callback.onTestComplete(result));
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Test execution failed", e);
                detailedLog.append("\n[ERROR] Test execution failed: ").append(e.getMessage());
                
                TestResult result = new TestResult(false, "Test execution failed", 
                    detailedLog.toString(), System.currentTimeMillis() - startTime);
                
                if (callback != null) {
                    new Handler(Looper.getMainLooper()).post(() -> callback.onTestComplete(result));
                }
            }
        }).start();
    }
    
    private static void updateProgress(TestCallback callback, String message) {
        Log.d(TAG, message);
        if (callback != null) {
            new Handler(Looper.getMainLooper()).post(() -> callback.onTestProgress(message));
        }
    }
    
    private static boolean testConfiguration(StringBuilder log) {
        log.append("\n=== Configuration Validation Test ===\n");
        
        try {
            boolean valid = AuthRecoveryUtil.validateAuthConfiguration();
            log.append("Configuration validation: ").append(valid ? "PASS" : "FAIL").append("\n");
            
            if (valid) {
                log.append("✅ KeyAuth API configuration is correct\n");
                log.append("✅ App hash matches BearMod-Loader: 60885ac0cf1061079d5756a689630d13\n");
                log.append("✅ All required constants are present\n");
            } else {
                log.append("❌ Configuration validation failed\n");
            }
            
            return valid;
            
        } catch (Exception e) {
            log.append("❌ Configuration test failed: ").append(e.getMessage()).append("\n");
            return false;
        }
    }
    
    private static boolean testHWIDGeneration(Context context, StringBuilder log) {
        log.append("\n=== HWID Generation Test ===\n");
        
        try {
            String hwid = AuthRecoveryUtil.verifyDeviceHWID(context);
            boolean valid = hwid != null && !hwid.equals("hwid_generation_failed") && hwid.length() == 32;
            
            log.append("HWID generation: ").append(valid ? "PASS" : "FAIL").append("\n");
            log.append("Generated HWID: ").append(hwid).append("\n");
            log.append("HWID length: ").append(hwid != null ? hwid.length() : 0).append(" (expected: 32)\n");
            
            if (valid) {
                log.append("✅ HWID generation successful\n");
                log.append("✅ HWID format is correct\n");
            } else {
                log.append("❌ HWID generation failed or invalid format\n");
            }
            
            return valid;
            
        } catch (Exception e) {
            log.append("❌ HWID test failed: ").append(e.getMessage()).append("\n");
            return false;
        }
    }
    
    private static boolean testAuthManagerInit(Context context, StringBuilder log) {
        log.append("\n=== Authentication Manager Initialization Test ===\n");
        
        try {
            CountDownLatch latch = new CountDownLatch(1);
            final boolean[] initSuccess = {false};
            final String[] initMessage = {""};
            
            BearModAuthManager authManager = BearModAuthManager.getInstance(context);
            authManager.initialize(new BearModAuthManager.AuthCallback() {
                @Override
                public void onSuccess(String message) {
                    initSuccess[0] = true;
                    initMessage[0] = message;
                    latch.countDown();
                }
                
                @Override
                public void onError(String error) {
                    initSuccess[0] = false;
                    initMessage[0] = error;
                    latch.countDown();
                }
            });
            
            boolean completed = latch.await(10, TimeUnit.SECONDS);
            
            log.append("Initialization completed: ").append(completed ? "YES" : "TIMEOUT").append("\n");
            log.append("Initialization result: ").append(initSuccess[0] ? "SUCCESS" : "FAILED").append("\n");
            log.append("Message: ").append(initMessage[0]).append("\n");
            
            if (completed && initSuccess[0]) {
                log.append("✅ Authentication manager initialized successfully\n");
            } else {
                log.append("❌ Authentication manager initialization failed\n");
            }
            
            return completed && initSuccess[0];
            
        } catch (Exception e) {
            log.append("❌ Initialization test failed: ").append(e.getMessage()).append("\n");
            return false;
        }
    }
    
    private static boolean testKeyAuthAuthentication(Context context, String licenseKey, StringBuilder log) {
        log.append("\n=== KeyAuth Authentication Test ===\n");
        
        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            log.append("❌ No license key provided for testing\n");
            return false;
        }
        
        try {
            CountDownLatch latch = new CountDownLatch(1);
            final boolean[] authSuccess = {false};
            final String[] authMessage = {""};
            
            BearModAuthManager authManager = BearModAuthManager.getInstance(context);
            authManager.authenticate(licenseKey, new BearModAuthManager.AuthCallback() {
                @Override
                public void onSuccess(String message) {
                    authSuccess[0] = true;
                    authMessage[0] = message;
                    latch.countDown();
                }
                
                @Override
                public void onError(String error) {
                    authSuccess[0] = false;
                    authMessage[0] = error;
                    latch.countDown();
                }
            });
            
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            
            log.append("Authentication completed: ").append(completed ? "YES" : "TIMEOUT").append("\n");
            log.append("Authentication result: ").append(authSuccess[0] ? "SUCCESS" : "FAILED").append("\n");
            log.append("Message: ").append(authMessage[0]).append("\n");
            log.append("License key: ").append(licenseKey.substring(0, Math.min(8, licenseKey.length()))).append("...\n");
            
            if (completed && authSuccess[0]) {
                log.append("✅ KeyAuth authentication successful\n");
            } else {
                log.append("❌ KeyAuth authentication failed\n");
            }
            
            return completed && authSuccess[0];
            
        } catch (Exception e) {
            log.append("❌ Authentication test failed: ").append(e.getMessage()).append("\n");
            return false;
        }
    }
    
    private static boolean testTokenGeneration(Context context, StringBuilder log) {
        log.append("\n=== Token Generation and Storage Test ===\n");
        
        try {
            SharedPreferences sharedPrefs = context.getSharedPreferences("bearmod_shared", Context.MODE_PRIVATE);
            
            String token = sharedPrefs.getString("bear_token", null);
            String signature = sharedPrefs.getString("bear_signature", null);
            long expiry = sharedPrefs.getLong("bear_expiry", 0);
            String deviceId = sharedPrefs.getString("bear_device", null);
            long created = sharedPrefs.getLong("bear_created", 0);
            
            boolean tokenExists = token != null && !token.isEmpty();
            boolean signatureExists = signature != null && !signature.isEmpty();
            boolean expiryValid = expiry > System.currentTimeMillis();
            boolean deviceIdExists = deviceId != null && !deviceId.isEmpty();
            boolean createdValid = created > 0;
            
            log.append("Token exists: ").append(tokenExists ? "YES" : "NO").append("\n");
            log.append("Signature exists: ").append(signatureExists ? "YES" : "NO").append("\n");
            log.append("Expiry valid: ").append(expiryValid ? "YES" : "NO").append("\n");
            log.append("Device ID exists: ").append(deviceIdExists ? "YES" : "NO").append("\n");
            log.append("Created timestamp valid: ").append(createdValid ? "YES" : "NO").append("\n");
            
            if (expiry > 0) {
                long timeToExpiry = expiry - System.currentTimeMillis();
                log.append("Time to expiry: ").append(timeToExpiry / 1000).append(" seconds\n");
            }
            
            boolean allValid = tokenExists && signatureExists && expiryValid && deviceIdExists && createdValid;
            
            if (allValid) {
                log.append("✅ BearToken generated and stored successfully\n");
            } else {
                log.append("❌ BearToken generation or storage failed\n");
            }
            
            return allValid;
            
        } catch (Exception e) {
            log.append("❌ Token test failed: ").append(e.getMessage()).append("\n");
            return false;
        }
    }
    
    private static boolean testBearModLoaderCompatibility(Context context, StringBuilder log) {
        log.append("\n=== BearMod-Loader Compatibility Test ===\n");
        
        try {
            // Check if shared preferences are accessible
            SharedPreferences sharedPrefs = context.getSharedPreferences("bearmod_shared", Context.MODE_PRIVATE);
            int keyCount = sharedPrefs.getAll().size();
            
            log.append("Shared preferences accessible: YES\n");
            log.append("Stored keys count: ").append(keyCount).append("\n");
            
            // Check authentication state
            BearModAuthManager authManager = BearModAuthManager.getInstance(context);
            boolean isAuthenticated = authManager.isAuthenticated();
            
            log.append("Authentication state: ").append(isAuthenticated ? "AUTHENTICATED" : "NOT AUTHENTICATED").append("\n");
            
            if (isAuthenticated && keyCount > 0) {
                log.append("✅ BearMod-Loader compatibility maintained\n");
                log.append("✅ Authentication data is accessible to target applications\n");
                return true;
            } else {
                log.append("❌ BearMod-Loader compatibility issues detected\n");
                return false;
            }
            
        } catch (Exception e) {
            log.append("❌ Compatibility test failed: ").append(e.getMessage()).append("\n");
            return false;
        }
    }
}
