plugins {
    id 'com.android.application'
}

android {
    compileSdk = 35
    namespace = 'com.bearmod'

    defaultConfig {
        applicationId = "com.bearmod"
        minSdk = 29
        //noinspection OldTargetApi
        targetSdk = 35
        versionCode = 1
        versionName = "3.8.0"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters += "arm64-v8a"
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
        disable 'InvalidPackage', 'MissingTranslation'
    }

    buildTypes {
        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig = signingConfigs.debug
        }
    }

    tasks.withType(JavaCompile).configureEach {
        options.compilerArgs.addAll(["-Xlint:unchecked", "-Xlint:deprecation"])
    }

    externalNativeBuild {
        ndkBuild {
            path "src/main/jni/Android.mk"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    ndkVersion = '27.1.12297006'
}

dependencies {
   
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation 'androidx.core:core:1.16.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.viewpager:viewpager:1.1.0'
    implementation 'androidx.fragment:fragment:1.8.7'
    implementation 'androidx.viewpager2:viewpager2:1.1.0'
    implementation 'androidx.exifinterface:exifinterface:1.4.1'

    implementation 'com.google.android.material:material:1.12.0'
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.google.android.material:material:1.12.0'
}
