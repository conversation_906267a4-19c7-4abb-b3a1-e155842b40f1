package com.bearmod.loader.auth;

import android.Manifest;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Base64;
import android.provider.Settings;
import android.os.Build;

import com.bearmod.loader.security.NativeSecurityManager;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.UUID;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;

import org.json.JSONObject;

/**
 * BearMod-Loader Compatible KeyAuth Integration Manager
 * Handles license validation and user authentication using KeyAuth API
 * Compatible with BearMod-Loader authentication system and BearToken generation
 */
public class BearModAuthManager {
    private static final String TAG = "BearModAuth";
    private static final String PREFS_NAME = "bearmod_auth";
    private static final String KEY_LICENSE = "license_key";
    private static final String KEY_IS_AUTHENTICATED = "is_authenticated";
    private static final String KEY_LAST_AUTH_TIME = "last_auth_time";
    private static final String KEY_SESSION_TOKEN = "session_token";
    private static final String KEY_USER_DATA = "user_data";
    private static final String KEY_BEAR_TOKEN = "bear_token";
    
    // KeyAuth Configuration - MUST match BearMod-Loader exactly
    private static final String KEYAUTH_API_URL = "https://keyauth.win/api/1.3/";
    private static final String KEYAUTH_APP_NAME = "com.bearmod.loader";
    private static final String KEYAUTH_OWNER_ID = "yLoA9zcOEF";
    private static final String KEYAUTH_APP_SECRET = "e99363a37eaa69acf4db6a6d4781fdf464cd4b429082de970a08436cac362d7d";
    private static final String KEYAUTH_VERSION = "1.3";
    private static final String KEYAUTH_APP_HASH = "60885ac0cf1061079d5756a689630d13"; // Fixed hash from BearMod-Loader

    // BearMod-Loader Compatibility
    private static final String SHARED_PREFS_NAME = "bearmod_shared";
    private static final String AES_KEY = "BearModLoader2024"; // Must match BearTokenManager exactly
    
    // Authentication validity period (24 hours)
    private static final long AUTH_VALIDITY_PERIOD = 24 * 60 * 60 * 1000;
    
    // Network timeouts
    private static final int CONNECT_TIMEOUT = 10000; // 10 seconds
    private static final int READ_TIMEOUT = 15000; // 15 seconds
    
    private static volatile BearModAuthManager instance;
    private final Context context;
    private final SharedPreferences prefs;
    private final SharedPreferences sharedPrefs;
    private final ExecutorService executor;
    private final Handler mainHandler;
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isAuthenticated = new AtomicBoolean(false);
    
    private BearModAuthManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.sharedPrefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);
        this.executor = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // Check if already authenticated
        checkExistingAuthentication();
    }
    
    public static synchronized BearModAuthManager getInstance(Context context) {
        if (instance == null) {
            instance = new BearModAuthManager(context);
        }
        return instance;
    }
    
    /**
     * Authentication callback interface
     */
    public interface AuthCallback {
        void onSuccess(String message);
        void onError(String error);
    }
    
    /**
     * Initialize the authentication manager
     */
    public void initialize(AuthCallback callback) {
        if (isInitialized.get()) {
            if (callback != null) {
                callback.onSuccess("Authentication manager already initialized");
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                Log.d(TAG, "Initializing BearMod authentication manager");
                
                // Perform initialization tasks
                Thread.sleep(500); // Simulate initialization
                
                isInitialized.set(true);
                Log.d(TAG, "Authentication manager initialized successfully");
                
                if (callback != null) {
                    mainHandler.post(() -> callback.onSuccess("Authentication manager initialized"));
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error initializing authentication manager", e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("Initialization failed: " + e.getMessage()));
                }
            }
        });
    }
    
    /**
     * Authenticate with license key using KeyAuth API
     */
    public void authenticate(String licenseKey, AuthCallback callback) {
        if (!isInitialized.get()) {
            if (callback != null) {
                callback.onError("Authentication manager not initialized");
            }
            return;
        }
        
        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("License key cannot be empty");
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                Log.d(TAG, "Authenticating with KeyAuth API");
                
                // Step 1: Initialize session with KeyAuth
                String sessionId = initializeKeyAuthSession();
                if (sessionId == null) {
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError("Failed to initialize authentication session"));
                    }
                    return;
                }
                
                // Step 2: Authenticate license key
                KeyAuthResponse authResponse = authenticateWithKeyAuth(sessionId, licenseKey);
                
                if (authResponse.success) {
                    // Step 3: Generate BearToken for compatibility
                    BearToken bearToken = generateBearToken(sessionId, licenseKey, authResponse.userData);
                    
                    // Save authentication state
                    saveAuthenticationState(licenseKey, sessionId, authResponse.userData);
                    
                    // Store BearToken for BearMod-Loader compatibility
                    storeBearToken(bearToken);
                    
                    isAuthenticated.set(true);
                    
                    Log.d(TAG, "KeyAuth authentication successful");
                    if (callback != null) {
                        mainHandler.post(() -> callback.onSuccess("Authentication successful"));
                    }
                } else {
                    Log.w(TAG, "KeyAuth authentication failed: " + authResponse.message);
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError(authResponse.message));
                    }
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error during KeyAuth authentication", e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("Authentication error: " + e.getMessage()));
                }
            }
        });
    }
    
    /**
     * Check if user is currently authenticated
     */
    public boolean isAuthenticated() {
        if (!isAuthenticated.get()) {
            return false;
        }
        
        // Check if authentication is still valid
        long lastAuthTime = prefs.getLong(KEY_LAST_AUTH_TIME, 0);
        return (System.currentTimeMillis() - lastAuthTime) < AUTH_VALIDITY_PERIOD;
    }
    
    /**
     * Logout user and clear authentication data
     */
    public void logout() {
        isAuthenticated.set(false);
        prefs.edit().clear().apply();
        sharedPrefs.edit().clear().apply();
        Log.d(TAG, "User logged out");
    }
    
    /**
     * Check existing authentication state
     */
    private void checkExistingAuthentication() {
        boolean wasAuthenticated = prefs.getBoolean(KEY_IS_AUTHENTICATED, false);
        long lastAuthTime = prefs.getLong(KEY_LAST_AUTH_TIME, 0);
        
        if (wasAuthenticated && (System.currentTimeMillis() - lastAuthTime) < AUTH_VALIDITY_PERIOD) {
            isAuthenticated.set(true);
            Log.d(TAG, "Found valid existing authentication");
        } else if (wasAuthenticated) {
            // Clear expired authentication
            prefs.edit().clear().apply();
            sharedPrefs.edit().clear().apply();
            Log.d(TAG, "Cleared expired authentication");
        }
    }
    
    /**
     * BearToken class for BearMod-Loader compatibility
     */
    public static class BearToken {
        public final String encryptedPayload;
        public final String signature;
        public final long createdTime;
        public final long expiryTime;
        public final String deviceId;
        
        public BearToken(String encryptedPayload, String signature, long createdTime, long expiryTime, String deviceId) {
            this.encryptedPayload = encryptedPayload;
            this.signature = signature;
            this.createdTime = createdTime;
            this.expiryTime = expiryTime;
            this.deviceId = deviceId;
        }
    }
    
    /**
     * KeyAuth Response class
     */
    private static class KeyAuthResponse {
        boolean success;
        String message;
        String userData;

        KeyAuthResponse(boolean success, String message, String userData) {
            this.success = success;
            this.message = message;
            this.userData = userData;
        }
    }

    /**
     * Initialize KeyAuth session
     */
    private String initializeKeyAuthSession() {
        try {
            URL url = new URL(KEYAUTH_API_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "BearMod-KeyAuth/1.0");

            // Create device HWID using BearMod-Loader compatible method
            String hwid = generateDeviceHWID();

            // Use fixed hash from BearMod-Loader instead of device HWID
            String apiUrl = KEYAUTH_API_URL + "?type=init&name=" + KEYAUTH_APP_NAME +
                           "&ownerid=" + KEYAUTH_OWNER_ID + "&ver=" + KEYAUTH_VERSION +
                           "&hash=" + KEYAUTH_APP_HASH;

            // Change to GET request like BearMod-Loader
            connection = (HttpURLConnection) new URL(apiUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestProperty("User-Agent", "KeyAuth");

            // Read response
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    JSONObject jsonResponse = new JSONObject(response.toString());
                    if (jsonResponse.getBoolean("success")) {
                        String sessionId = jsonResponse.getString("sessionid");
                        Log.d(TAG, "KeyAuth session initialized successfully");
                        return sessionId;
                    } else {
                        Log.e(TAG, "KeyAuth init failed: " + jsonResponse.getString("message"));
                    }
                }
            } else {
                Log.e(TAG, "KeyAuth init HTTP error: " + responseCode);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error initializing KeyAuth session", e);
        }

        return null;
    }

    /**
     * Authenticate with KeyAuth API
     */
    private KeyAuthResponse authenticateWithKeyAuth(String sessionId, String licenseKey) {
        try {
            // Create device HWID using BearMod-Loader compatible method
            String hwid = generateDeviceHWID();

            // Use GET request with query parameters like BearMod-Loader
            String apiUrl = KEYAUTH_API_URL + "?type=license&key=" + licenseKey +
                           "&sessionid=" + sessionId + "&name=" + KEYAUTH_APP_NAME +
                           "&ownerid=" + KEYAUTH_OWNER_ID + "&hwid=" + hwid;

            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestProperty("User-Agent", "KeyAuth");

            // Read response
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    JSONObject jsonResponse = new JSONObject(response.toString());
                    boolean success = jsonResponse.getBoolean("success");
                    String message = jsonResponse.optString("message", "Unknown error");
                    String userData = jsonResponse.optString("info", "");

                    return new KeyAuthResponse(success, message, userData);
                }
            } else {
                return new KeyAuthResponse(false, "HTTP error: " + responseCode, "");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error authenticating with KeyAuth", e);
            return new KeyAuthResponse(false, "Network error: " + e.getMessage(), "");
        }
    }

    /**
     * Generate device HWID using EXACT BearMod-Loader algorithm
     * Must match BearMod-Loader's generateHWID method exactly
     */
    private String generateDeviceHWID() {
        try {
            String serial;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (context.checkSelfPermission(Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
                    serial = Build.getSerial();
                } else {
                    serial = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
                }
            } else {
                // For API < 26, use Android ID as fallback instead of deprecated Build.SERIAL
                serial = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            }

            // Combine identifiers to create unique HWID (EXACT match with BearMod-Loader)
            String combined = serial + "-" + Build.FINGERPRINT;

            // Hash the combined string for consistency and privacy
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(combined.getBytes(StandardCharsets.UTF_8));

            // Convert to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString().substring(0, 32); // Return first 32 characters like BearMod-Loader

        } catch (Exception e) {
            Log.e(TAG, "Error generating HWID: " + e.getMessage(), e);
            // Fallback to a device-specific identifier
            return Build.FINGERPRINT.hashCode() + "";
        }
    }

    /**
     * Generate BearToken for EXACT BearMod-Loader compatibility
     */
    private BearToken generateBearToken(String sessionId, String licenseKey, String userData) {
        try {
            Log.d(TAG, "Generating BearToken with BearMod-Loader compatibility");

            // Verify loader signature first (like BearMod-Loader)
            if (!NativeSecurityManager.verifyLoaderSignature(context)) {
                Log.e(TAG, "Loader signature verification failed");
                return null;
            }

            // Generate token components
            long currentTime = System.currentTimeMillis();
            long expiryTime = currentTime + AUTH_VALIDITY_PERIOD;
            String deviceId = getDeviceId(); // Use BearMod-Loader compatible device ID
            String loaderSignature = NativeSecurityManager.getSignatureHash(context, context.getPackageName());

            // Create token payload EXACTLY like BearMod-Loader
            String tokenPayload = String.format("%s|%s|%s|%d|%d|%s",
                sessionId,
                hashString(licenseKey), // Hash license key for privacy
                deviceId,
                currentTime,
                expiryTime,
                loaderSignature
            );

            // Encrypt token payload using AES like BearMod-Loader
            String encryptedToken = encryptString(tokenPayload);

            // Generate signature for integrity
            String tokenSignature = generateTokenSignature(encryptedToken, loaderSignature);

            // Create BearToken object
            BearToken bearToken = new BearToken(
                encryptedToken,
                tokenSignature,
                currentTime,
                expiryTime,
                deviceId
            );

            Log.d(TAG, "BearToken generated successfully");
            return bearToken;

        } catch (Exception e) {
            Log.e(TAG, "Failed to generate BearToken", e);
            return null;
        }
    }

    /**
     * Store BearToken in shared location for BearMod-Loader compatibility
     */
    private void storeBearToken(BearToken token) {
        if (token == null) return;

        try {
            // Store in shared preferences accessible by target mods
            SharedPreferences.Editor editor = sharedPrefs.edit();

            editor.putString("bear_token", token.encryptedPayload);
            editor.putString("bear_signature", token.signature);
            editor.putLong("bear_expiry", token.expiryTime);
            editor.putString("bear_device", token.deviceId);
            editor.putLong("bear_created", token.createdTime);

            editor.apply();

            Log.d(TAG, "BearToken stored in shared location for BearMod-Loader compatibility");

        } catch (Exception e) {
            Log.e(TAG, "Failed to store shared BearToken", e);
        }
    }

    /**
     * Save authentication state with session data
     */
    private void saveAuthenticationState(String licenseKey, String sessionId, String userData) {
        prefs.edit()
                .putString(KEY_LICENSE, licenseKey)
                .putString(KEY_SESSION_TOKEN, sessionId)
                .putString(KEY_USER_DATA, userData)
                .putBoolean(KEY_IS_AUTHENTICATED, true)
                .putLong(KEY_LAST_AUTH_TIME, System.currentTimeMillis())
                .apply();
    }

    /**
     * Hash string using SHA-256 (BearMod-Loader compatible)
     */
    private String hashString(String input) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(input.getBytes());

        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * Get device ID for token binding (BearMod-Loader compatible)
     */
    private String getDeviceId() {
        // Use a combination of device identifiers (avoid deprecated Build.SERIAL)
        String deviceInfo = Build.FINGERPRINT +
                           Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID) +
                           Build.ID;
        try {
            return hashString(deviceInfo).substring(0, 16);
        } catch (Exception e) {
            return "unknown_device";
        }
    }

    /**
     * Generate token signature for integrity verification (BearMod-Loader compatible)
     */
    private String generateTokenSignature(String encryptedPayload, String loaderSignature) throws Exception {
        String signatureInput = encryptedPayload + "|" + loaderSignature + "|" + AES_KEY;
        return hashString(signatureInput);
    }

    /**
     * Encrypt string using AES (BearMod-Loader compatible)
     */
    private String encryptString(String plaintext) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");

        // Generate random IV
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plaintext.getBytes());

        // Combine IV + encrypted data
        byte[] combined = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);

        return Base64.encodeToString(combined, Base64.DEFAULT);
    }
}
