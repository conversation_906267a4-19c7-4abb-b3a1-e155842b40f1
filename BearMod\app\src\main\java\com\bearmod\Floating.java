package com.bearmod;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.IBinder;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.util.Base64;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.widget.ImageView;
import android.view.MotionEvent;
import android.os.Vibrator;
import android.view.ViewGroup;
import android.widget.TextView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import android.graphics.Color;
import android.view.Gravity;
import android.widget.Toast;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;

import android.os.VibratorManager;
import android.os.VibrationEffect;
import android.content.Context;

import android.util.Log;
import android.os.Build;

public class Floating extends FloatingService {
    private MenuUIManager menuUIManager;
    private ConfigurationManager configManager;
    private LinearLayout mainLayout;
    private RelativeLayout iconLayout;
    private ESPView canvasLayout;
    private WindowManager.LayoutParams iconLayoutParams;

    private int currentMenu = 0; // 0: Main, 1: ESP, 2: Aim, 3: Skin, 4: Hide

    private TextView updateNotificationText;

    @Override
    public void onCreate() {
        super.onCreate();
        System.loadLibrary("bearmod");
        configManager = new ConfigurationManager(this, this::onConfigChanged);
        createLayouts();
        menuUIManager = new MenuUIManager(this, mainLayout, configManager);
        setupTouchHandling();
        createCanvas();
        createFloatingIcon();
        showMenu(0); // Show Main Menu by default
    }

    private void createLayouts() {
        Context themedContext = new ContextThemeWrapper(this, R.style.FloatingServiceTheme);
        mainLayout = new LinearLayout(themedContext);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        iconLayout = new RelativeLayout(themedContext);
        windowManager.addView(mainLayout, mainLayoutParams);
        windowManager.addView(iconLayout, createIconLayoutParams());
    }

    private WindowManager.LayoutParams createIconLayoutParams() {
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
            dpToPx(48),
            dpToPx(48),
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        );
        params.gravity = android.view.Gravity.START | android.view.Gravity.TOP;
        return params;
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setupTouchHandling() {
        mainLayout.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    // Store initial positions
                    initialX = mainLayoutParams.x;
                    initialY = mainLayoutParams.y;
                    initialTouchX = event.getRawX();
                    initialTouchY = event.getRawY();
                    return true;

                case MotionEvent.ACTION_MOVE:
                    // Calculate new position
                    float deltaX = event.getRawX() - initialTouchX;
                    float deltaY = event.getRawY() - initialTouchY;
                    float newX = initialX + deltaX;
                    float newY = initialY + deltaY;

                    // Constrain to screen bounds
                    float maxX = screenWidth - v.getWidth();
                    float maxY = screenHeight - v.getHeight();
                    newX = Math.max(0, Math.min(maxX, newX));
                    newY = Math.max(0, Math.min(maxY, newY));

                    // Update layout
                    mainLayoutParams.x = (int) newX;
                    mainLayoutParams.y = (int) newY;
                    windowManager.updateViewLayout(mainLayout, mainLayoutParams);
                    return true;

                case MotionEvent.ACTION_UP:
                    // Handle click if minimal movement
                    if (Math.abs(event.getRawX() - initialTouchX) < 10 && 
                        Math.abs(event.getRawY() - initialTouchY) < 10) {
                        v.performClick();
                    }
                    return true;
            }
            return false;
        });
    }

    // Add member variables for touch handling
    private float initialX;
    private float initialY;
    private float initialTouchX;
    private float initialTouchY;
    private void createCanvas() {
        canvasLayout = new ESPView(this);
        WindowManager.LayoutParams canvasParams = new WindowManager.LayoutParams(
            screenWidth,
            screenHeight,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE |
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        );
        canvasParams.gravity = android.view.Gravity.TOP | android.view.Gravity.START;
        windowManager.addView(canvasLayout, canvasParams);
    }

    private void createFloatingIcon() {
        iconLayout = new RelativeLayout(this);
        RelativeLayout.LayoutParams iconParams = new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        iconLayout.setLayoutParams(iconParams);

        ImageView iconImg = new ImageView(this);
        // px, can be adjusted
        int iconSize = 120;
        ViewGroup.LayoutParams iconImgParams = new ViewGroup.LayoutParams(iconSize, iconSize);
        iconImg.setLayoutParams(iconImgParams);
        iconImg.setScaleType(ImageView.ScaleType.CENTER_CROP);
        iconImg.setBackgroundResource(android.R.drawable.dialog_holo_light_frame); // Modern rounded bg

        // Load icon from native base64
        try {
            String iconBase64 = iconenc();
            byte[] iconData = Base64.decode(iconBase64, Base64.DEFAULT);
            Bitmap bmp = BitmapFactory.decodeByteArray(iconData, 0, iconData.length);
            iconImg.setImageBitmap(bmp);
        } catch (Exception e) {
            android.util.Log.e("Floating", "Error in createFloatingIcon", e);
        }

        iconLayout.addView(iconImg);

        iconLayoutParams = new WindowManager.LayoutParams();
        iconLayoutParams.width = iconSize;
        iconLayoutParams.height = iconSize;
        iconLayoutParams.type = type;
        iconLayoutParams.format = PixelFormat.RGBA_8888;
        iconLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        iconLayoutParams.gravity = android.view.Gravity.START | android.view.Gravity.TOP;
        iconLayoutParams.x = 0;
        iconLayoutParams.y = 0;
        iconLayoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        iconLayout.setVisibility(View.VISIBLE);

        iconLayout.setOnTouchListener(new View.OnTouchListener() {
            private int initialIconX;
            private int initialIconY;
            private float initialTouchX;
            private float initialTouchY;
            private boolean isDragging;

            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        initialIconX = iconLayoutParams.x;
                        initialIconY = iconLayoutParams.y;
                        initialTouchX = event.getRawX();
                        initialTouchY = event.getRawY();
                        isDragging = false;
                        try {
                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                                VibratorManager vibratorManager = (VibratorManager) getSystemService(Context.VIBRATOR_MANAGER_SERVICE);
                                if (vibratorManager != null) {
                                    Vibrator vibrator = vibratorManager.getDefaultVibrator();
                                    if (vibrator != null) {
                                        vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE));
                                    }
                                }
                            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
                                if (vibrator != null) {
                                    vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE));
                                }
                            } else {
                                Vibrator vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
                                if (vibrator != null) {
                                    vibrator.vibrate(50);
                                }
                            }
                        } catch (Exception e) {
                            Log.e("Floating", "Vibration error", e);
                        }
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        float deltaX = event.getRawX() - initialTouchX;
                        float deltaY = event.getRawY() - initialTouchY;

                        if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
                            isDragging = true;
                            float newX = initialIconX + deltaX;
                            float newY = initialIconY + deltaY;

                            // Constrain to screen bounds
                            float maxX = screenWidth - v.getWidth();
                            float maxY = screenHeight - v.getHeight();
                            newX = Math.max(0, Math.min(maxX, newX));
                            newY = Math.max(0, Math.min(maxY, newY));

                            iconLayoutParams.x = (int) newX;
                            iconLayoutParams.y = (int) newY;
                            mainLayoutParams.x = iconLayoutParams.x; // Keep mainLayout aligned with icon
                            mainLayoutParams.y = iconLayoutParams.y;
                            try {
                                windowManager.updateViewLayout(iconLayout, iconLayoutParams);
                                windowManager.updateViewLayout(mainLayout, mainLayoutParams);
                            } catch (IllegalArgumentException e) {
                                Log.e("Floating", "Error updating view layout: " + e.getMessage());
                            }
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                        if (!isDragging) {
                            // It's a tap
                            if (mainLayout.getVisibility() == View.VISIBLE) {
                                mainLayout.setVisibility(View.GONE);
                                iconLayout.setVisibility(View.VISIBLE);
                                Toast.makeText(Floating.this, "Menu closed", Toast.LENGTH_SHORT).show();
                            } else {
                                mainLayout.setVisibility(View.VISIBLE);
                                iconLayout.setVisibility(View.GONE);
                                Toast.makeText(Floating.this, "Menu opened", Toast.LENGTH_SHORT).show();
                            }
                        }
                        return true;
                }
                return false;
            }
        });

        windowManager.addView(iconLayout, iconLayoutParams);
    }

    // Show the selected menu (0: Main, 1: ESP, ...)
    private void showMenu(int menuIndex) {
        currentMenu = menuIndex;
        menuUIManager.clearContent();
        addMenuSidebar();
        switch (menuIndex) {
            case 0:
                addMainMenuContent();
                break;
            case 1:
                addEspMenuContent();
                break;
            case 2:
                addAimMenuContent();
                break;
            case 3:
                addSkinMenuContent();
                break;
            case 4:
                addHideMenuContent();
                break;
        }
    }

    // Sidebar for menu navigation
    private void addMenuSidebar() {
        LinearLayout sidebar = new LinearLayout(this);
        sidebar.setOrientation(LinearLayout.VERTICAL);
        sidebar.setBackgroundColor(0xFF2C2F33);
        sidebar.setPadding(dpToPx(8), dpToPx(8), dpToPx(8), dpToPx(8));
        String[] menuKeys = {"Main Menu", "Esp Menu", "Aim Menu", "Skin Menu", "Hide Menu"};
        for (int i = 0; i < menuKeys.length; i++) {
            final int idx = i;
            menuUIManager.addModernSwitch(LanguageManager.get(menuKeys[i]), currentMenu == i, isChecked -> {
                if (isChecked && currentMenu != idx) {
                    showMenu(idx);
                }
            });
        }
    }

    // Main Menu content
    private void addMainMenuContent() {
        // Language selector using Material Chips
        ChipGroup chipGroup = new ChipGroup(this);
        chipGroup.setChipSpacing(dpToPx(8));
        chipGroup.setSingleSelection(true);
        chipGroup.setPadding(dpToPx(18), dpToPx(8), dpToPx(18), dpToPx(8));

        Chip enChip = new Chip(this);
        enChip.setText("English");
        enChip.setCheckable(true);
        enChip.setChipBackgroundColorResource(android.R.color.darker_gray);
        enChip.setTextColor(0xFFFFFFFF);

        Chip zhChip = new Chip(this);
        zhChip.setText("中文");
        zhChip.setCheckable(true);
        zhChip.setChipBackgroundColorResource(android.R.color.darker_gray);
        zhChip.setTextColor(0xFFFFFFFF);

        Chip ruChip = new Chip(this);
        ruChip.setText("Русский");
        ruChip.setCheckable(true);
        ruChip.setChipBackgroundColorResource(android.R.color.darker_gray);
        ruChip.setTextColor(0xFFFFFFFF);

        chipGroup.addView(enChip);
        chipGroup.addView(zhChip);
        chipGroup.addView(ruChip);

        // Set initial selection
        String currentLang = LanguageManager.getCurrentLanguage();
        if ("zh".equals(currentLang)) {
            zhChip.setChecked(true);
        } else if ("ru".equals(currentLang)) {
            ruChip.setChecked(true);
        } else {
            enChip.setChecked(true);
        }

        chipGroup.setOnCheckedStateChangeListener((group, checkedIds) -> {
            if (!checkedIds.isEmpty()) {
                int checkedId = checkedIds.get(0);
                if (checkedId == enChip.getId()) {
                    LanguageManager.setLanguage("en");
                } else if (checkedId == zhChip.getId()) {
                    LanguageManager.setLanguage("zh");
                } else if (checkedId == ruChip.getId()) {
                    LanguageManager.setLanguage("ru");
                }
                showMenu(0); // Refresh menu
            }
        });

        // Update notification text
        updateNotificationText = new TextView(this);
        updateNotificationText.setTextColor(Color.RED);
        updateNotificationText.setTextSize(16);
        updateNotificationText.setGravity(Gravity.CENTER);
        updateNotificationText.setPadding(dpToPx(18), dpToPx(8), dpToPx(18), dpToPx(8));
        updateNotificationText.setVisibility(View.GONE); // Initially hidden
        updateNotificationText.setText(LanguageManager.get("update_available"));

        // Add click listener to the notification text (optional)
        updateNotificationText.setOnClickListener(v -> {
            // TODO: Add logic to handle update download/process when clicked
            // For now, just a placeholder:
            Toast.makeText(this, "Update notification clicked (fake logic)", Toast.LENGTH_SHORT).show();
        });

        menuUIManager.addViewToMainLayout(updateNotificationText); // Add notification text before chips
        menuUIManager.addViewToMainLayout(chipGroup); // Add chips after notification text
    }

    // ESP Menu content
    private void addEspMenuContent() {
        menuUIManager.addModernSwitch(LanguageManager.get("Line"),
            configManager.getBoolean("ESP_LINE", false),
            isChecked -> {
                configManager.updateConfig("ESP_LINE", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Bone"),
            configManager.getBoolean("ESP_BONE", false),
            isChecked -> {
                configManager.updateConfig("ESP_BONE", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Info"),
            configManager.getBoolean("ESP_INFO", false),
            isChecked -> {
                configManager.updateConfig("ESP_INFO", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Weapon"),
            configManager.getBoolean("ESP_WEAPON", false),
            isChecked -> {
                configManager.updateConfig("ESP_WEAPON", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Grenade Warning"),
            configManager.getBoolean("ESP_WARNING", false),
            isChecked -> {
                configManager.updateConfig("ESP_WARNING", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("360° Alert"),
            configManager.getBoolean("ESP_ALERT", false),
            isChecked -> {
                configManager.updateConfig("ESP_ALERT", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Radar MAP"),
            configManager.getBoolean("ESP_RADAR", false),
            isChecked -> {
                configManager.updateConfig("ESP_RADAR", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("IgnoreBot-ESP"),
            configManager.getBoolean("ESP_IGNOREBOTS", false),
            isChecked -> {
                configManager.updateConfig("ESP_IGNOREBOTS", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addSeekBar(LanguageManager.get("RadarMAP-Size"), "RADAR_SIZE", 60, 350, configManager.getInteger("RADAR_SIZE", 60), null);
    }

    // Aim Menu content
    private void addAimMenuContent() {
        menuUIManager.addModernSwitch(LanguageManager.get("AimBot"),
            configManager.getBoolean("NRG_AIMBOT", false),
            isChecked -> {
                configManager.updateConfig("NRG_AIMBOT", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addCombo(LanguageManager.get("Aim-Target"), new String[]{LanguageManager.get("Head"), LanguageManager.get("Body")}, "AIM_TARGET", position -> {
            configManager.updateConfig("AIM_TARGET", position);
            Switch(position);
            configManager.saveConfiguration();
        });
        menuUIManager.addCombo(LanguageManager.get("Aim-Trigger"), new String[]{LanguageManager.get("Shoot"), LanguageManager.get("Scope"), LanguageManager.get("Both")}, "AIM_TRIGGER", position -> {
            configManager.updateConfig("AIM_TRIGGER", position);
            Switch(position);
            configManager.saveConfiguration();
        });
        menuUIManager.addModernSwitch(LanguageManager.get("Aim-IgnoreBot"),
            configManager.getBoolean("AIM_IGNOREBOTS", false),
            isChecked -> {
                configManager.updateConfig("AIM_IGNOREBOTS", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Aim-Knocked"),
            configManager.getBoolean("AIM_KNOCKED", false),
            isChecked -> {
                configManager.updateConfig("AIM_KNOCKED", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("iPad View"),
            configManager.getBoolean("MEMORY_WIDEVIEW", false),
            isChecked -> {
                configManager.updateConfig("MEMORY_WIDEVIEW", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("VisiCheck"),
            configManager.getBoolean("AIM_VISCHECK", false),
            isChecked -> {
                configManager.updateConfig("AIM_VISCHECK", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Aim-Recoil"),
            configManager.getBoolean("RECOI_LCOMPARISON", false),
            isChecked -> {
                configManager.updateConfig("RECOI_LCOMPARISON", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addSeekBar(LanguageManager.get("RecoilSize"), "RECOIL_SIZE", 0, 5, configManager.getInteger("RECOIL_SIZE", 1), null);
        menuUIManager.addSeekBar(LanguageManager.get("Aim-Dist"), "AIM_DISTANCE", 0, 180, configManager.getInteger("AIM_DISTANCE", 1), null);
        menuUIManager.addSeekBar(LanguageManager.get("Fov-Aim"), "AIM_SIZE", 50, 350, configManager.getInteger("AIM_SIZE", 1), null);
    }

    // Skin Menu content
    private void addSkinMenuContent() {
        menuUIManager.addModernSwitch(LanguageManager.get("Skin-Enable"),
            configManager.getBoolean("SKIN_ENABLE", false),
            isChecked -> {
                configManager.updateConfig("SKIN_ENABLE", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("DeadBox (Ingame for open)"),
            configManager.getBoolean("SKIN_BOXENABLE", false),
            isChecked -> {
                configManager.updateConfig("SKIN_BOXENABLE", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );

        menuUIManager.addSeekBar(LanguageManager.get("X-suit"), "SKIN_XSUIT", 0, 13, configManager.getInteger("SKIN_XSUIT", 0), null);
        menuUIManager.addSeekBar(LanguageManager.get("Set"), "SKIN_SET", 0, 72, configManager.getInteger("SKIN_SET", 0), null);
        menuUIManager.addSeekBar(LanguageManager.get("Skin-BackPack"), "SKIN_BACKPACK", 0, 16, configManager.getInteger("SKIN_BACKPACK", 0), null);
        menuUIManager.addSeekBar(LanguageManager.get("Skin-Helmet"), "SKIN_HELMET", 0, 10, configManager.getInteger("SKIN_HELMET", 0), value -> Switch(1));

        // Weapon Skins
        menuUIManager.addSeekBar(LanguageManager.get("M416"), "SKIN_M416", 0, 11, configManager.getInteger("SKIN_M416", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("AKM"), "SKIN_AKM", 0, 10, configManager.getInteger("SKIN_AKM", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("SCAR-L"), "SKIN_SCARL", 0, 7, configManager.getInteger("SKIN_SCARL", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("M762"), "SKIN_M762", 0, 9, configManager.getInteger("SKIN_M762", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("M16A4"), "SKIN_M16A4", 0, 5, configManager.getInteger("SKIN_M16A4", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("GROZAR"), "SKIN_GROZAR", 0, 7, configManager.getInteger("SKIN_GROZAR", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("AUG"), "SKIN_AUG", 0, 5, configManager.getInteger("SKIN_AUG", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("ACE32"), "SKIN_ACE32", 0, 3, configManager.getInteger("SKIN_ACE32", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("M249"), "SKIN_M249", 0, 4, configManager.getInteger("SKIN_M249", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("DP28"), "SKIN_DP28", 0, 4, configManager.getInteger("SKIN_DP28", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("MG3"), "SKIN_MG3", 0, 1, configManager.getInteger("SKIN_MG3", 0), value -> Switch(1));

        // SMG Skins
        menuUIManager.addSeekBar(LanguageManager.get("P90"), "SKIN_P90", 0, 1, configManager.getInteger("SKIN_P90", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("UZI"), "SKIN_UZI", 0, 6, configManager.getInteger("SKIN_UZI", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("UMP45"), "SKIN_UMP45", 0, 8, configManager.getInteger("SKIN_UMP45", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("VECTOR"), "SKIN_VECTOR", 0, 4, configManager.getInteger("SKIN_VECTOR", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("THOMPSON"), "SKIN_THOMPSON", 0, 4, configManager.getInteger("SKIN_THOMPSON", 0), value -> Switch(1));

        // Sniper Skins
        menuUIManager.addSeekBar(LanguageManager.get("M24"), "SKIN_M24", 0, 5, configManager.getInteger("SKIN_M24", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("KAR98K"), "SKIN_KAR98K", 0, 6, configManager.getInteger("SKIN_KAR98K", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("AWM"), "SKIN_AWM", 0, 7, configManager.getInteger("SKIN_AWM", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("AMR"), "SKIN_AMR", 0, 1, configManager.getInteger("SKIN_AMR", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("MK14"), "SKIN_MK14", 0, 2, configManager.getInteger("SKIN_MK14", 0), value -> Switch(1));

        // Vehicle Skins
        menuUIManager.addSeekBar(LanguageManager.get("Dacia"), "SKIN_DACIA", 0, 23, configManager.getInteger("SKIN_DACIA", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("CoupeRP"), "SKIN_COUPERP", 0, 35, configManager.getInteger("SKIN_COUPERP", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("UAZ"), "SKIN_UAZ", 0, 13, configManager.getInteger("SKIN_UAZ", 0), value -> Switch(1));
        menuUIManager.addSeekBar(LanguageManager.get("Moto"), "SKIN_MOTO", 0, 8, configManager.getInteger("SKIN_MOTO", 0), value -> Switch(1));
    }

    // Hide Menu content
    private void addHideMenuContent() {
        menuUIManager.addModernSwitch(LanguageManager.get("livestream_mode"),
            configManager.getBoolean("RECORDER_HIDE", false),
            isChecked -> {
                configManager.updateConfig("RECORDER_HIDE", isChecked ? 1 : 0);
                if (isChecked) {
                    RecorderFakeUtils.setFakeRecorderWindowLayoutParams(
                        mainLayoutParams, mainLayoutParams, mainLayoutParams,
                        windowManager, mainLayout, iconLayout, canvasLayout, this);
                } else {
                    RecorderFakeUtils.unsetFakeRecorderWindowLayoutParams(
                        mainLayoutParams, mainLayoutParams, mainLayoutParams,
                        windowManager, mainLayout, iconLayout, canvasLayout, this);
                }
                configManager.saveConfiguration();
            }
        );

        menuUIManager.addModernSwitch(LanguageManager.get("Hide ESP"),
            configManager.getBoolean("HIDE_ESP", false),
            isChecked -> {
                configManager.updateConfig("HIDE_ESP", isChecked ? 1 : 0);
                Switch(isChecked ? 1 : 0);
                configManager.saveConfiguration();
            }
        );
        menuUIManager.addModernSwitch(LanguageManager.get("Hide Menu"),
            configManager.getBoolean("HIDE_MENU", false),
            isChecked -> {
                configManager.updateConfig("HIDE_MENU", isChecked ? 1 : 0);
                if (isChecked) {
                    mainLayout.setVisibility(View.GONE);
                    iconLayout.setVisibility(View.VISIBLE);
                }
                configManager.saveConfiguration();
            }
        );
    }

    private void onConfigChanged(String key, String value) {
        // Handle configuration changes
        if (key.startsWith("ESP_")) {
            Switch(Integer.parseInt(value));
        }
    }

    @Override
    protected void cleanup() {
        if (mainLayout != null && mainLayout.getParent() != null) {
            windowManager.removeView(mainLayout);
        }
        if (iconLayout != null && iconLayout.getParent() != null) {
            windowManager.removeView(iconLayout);
        }
        if (canvasLayout != null && canvasLayout.getParent() != null) {
            windowManager.removeView(canvasLayout);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    // FloatingTouchHandler.TouchCallback implementation
    @Override
    public void onSingleTap() {
        menuUIManager.setVisibility(View.VISIBLE);
        iconLayout.setVisibility(View.GONE);
    }

    @Override
    public void onDoubleTap() {
        menuUIManager.setVisibility(View.GONE);
        iconLayout.setVisibility(View.VISIBLE);
    }

    @Override
    public void onDragStart() {
        // Optional: add visual feedback for drag start
    }

    @Override
    public void onDragEnd() {
        // Optional: add visual feedback for drag end
    }

    // Native methods
    private native String ChannelName();
    private native String FeedBackName();
    private native String channellink();
    private native String feedbacklink();
    private native String onlinename();
    public native void Switch(int i);
    public static native void DrawOn(ESPView espView, android.graphics.Canvas canvas);
    private native String iconenc();
    private native boolean IsHideEsp();
    private native String cfg();
    private native void onSendConfig(String s, String v);

    // Placeholder for update notification logic (to be connected to loader/KeyAuth API later)
    public void showUpdateNotification() {
        // TODO: Implement actual logic to check for updates and show notification based on loader/KeyAuth.
        // This is a temporary placeholder to allow the project to build.
        updateNotificationText.setVisibility(View.VISIBLE);

        // Placeholder click listener
        updateNotificationText.setOnClickListener(v -> {
            // TODO: Implement action when update notification is clicked (e.g., open update screen).
            android.util.Log.d("Floating", "Update notification clicked (placeholder)");
            android.widget.Toast.makeText(this, "Update notification clicked (placeholder)", android.widget.Toast.LENGTH_SHORT).show();
        });
    }

    // Public method to hide the update notification (optional)
    public void hideUpdateNotification() {
        if (updateNotificationText != null) {
            updateNotificationText.setVisibility(View.GONE);
        }
    }
}