package com.bearmod.loader.target;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Build;
import android.util.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Utility class to verify the application signature
 */
public class SignatureVerifier {
    private static final String TAG = "SignatureVerifier";

    /**
     * Verifies if the application signature is valid
     * 
     * @param context Application context
     * @return true if signature is valid, false otherwise
     */
    public static boolean isSignatureValid(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo;
            Signature signature;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9+ (API 28+) - Use GET_SIGNING_CERTIFICATES
                packageInfo = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNING_CERTIFICATES);

                if (packageInfo.signingInfo == null) {
                    Log.e(TAG, "No signing info found");
                    return false;
                }

                Signature[] signatures;
                if (packageInfo.signingInfo.hasMultipleSigners()) {
                    signatures = packageInfo.signingInfo.getApkContentsSigners();
                } else {
                    signatures = packageInfo.signingInfo.getSigningCertificateHistory();
                }

                if (signatures == null || signatures.length == 0) {
                    Log.e(TAG, "No signatures found");
                    return false;
                }

                signature = signatures[0];
            } else {
                // Android 8.1 and below - Use legacy GET_SIGNATURES
                packageInfo = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);

                if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                    Log.e(TAG, "No signatures found");
                    return false;
                }

                signature = packageInfo.signatures[0];
            }

            // Get signature hash
            String signatureHash = getSignatureHash(signature);
            Log.d(TAG, "Signature hash: " + signatureHash);
            
            // In a real app, you would compare this hash with a hardcoded expected hash
            // For this example, we'll just check if the signature exists
            return signature != null && signature.toByteArray().length > 0;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found", e);
            return false;
        }
    }
    
    /**
     * Gets the SHA-256 hash of the signature
     * 
     * @param signature Application signature
     * @return SHA-256 hash of the signature
     */
    public static String getSignatureHash(Signature signature) {
        try {
            // Get signature bytes
            byte[] signatureBytes = signature.toByteArray();
            
            // Create SHA-256 digest
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] digest = md.digest(signatureBytes);
            
            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "SHA-256 algorithm not found", e);
            return "";
        }
    }
    
    /**
     * Gets the signature as a hex string
     * 
     * @param context Application context
     * @return Signature as hex string
     */
    public static String getSignatureHex(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo;
            Signature signature;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9+ (API 28+) - Use GET_SIGNING_CERTIFICATES
                packageInfo = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNING_CERTIFICATES);

                if (packageInfo.signingInfo == null) {
                    return "No signing info found";
                }

                Signature[] signatures;
                if (packageInfo.signingInfo.hasMultipleSigners()) {
                    signatures = packageInfo.signingInfo.getApkContentsSigners();
                } else {
                    signatures = packageInfo.signingInfo.getSigningCertificateHistory();
                }

                if (signatures == null || signatures.length == 0) {
                    return "No signatures found";
                }

                signature = signatures[0];
            } else {
                // Android 8.1 and below - Use legacy GET_SIGNATURES
                packageInfo = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);

                if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                    return "No signatures found";
                }

                signature = packageInfo.signatures[0];
            }
            byte[] signatureBytes = signature.toByteArray();
            
            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : signatureBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found", e);
            return "Error: " + e.getMessage();
        }
    }
}
