package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.provider.Settings;
import android.os.Build;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.UUID;

import org.json.JSONObject;

/**
 * KeyAuth Integration Manager for BearMod
 * Handles license validation and user authentication using KeyAuth API
 * Designed for stability, security, and crash prevention
 */
public class BearModAuthManager {
    private static final String TAG = "BearModAuth";
    private static final String PREFS_NAME = "bearmod_auth";
    private static final String KEY_LICENSE = "license_key";
    private static final String KEY_IS_AUTHENTICATED = "is_authenticated";
    private static final String KEY_LAST_AUTH_TIME = "last_auth_time";
    private static final String KEY_SESSION_TOKEN = "session_token";
    private static final String KEY_USER_DATA = "user_data";

    // KeyAuth Configuration
    private static final String KEYAUTH_API_URL = "https://keyauth.win/api/1.2/";
    private static final String KEYAUTH_APP_NAME = "BearMod";
    private static final String KEYAUTH_OWNER_ID = "your_owner_id"; // Replace with actual owner ID
    private static final String KEYAUTH_APP_SECRET = "your_app_secret"; // Replace with actual secret
    private static final String KEYAUTH_VERSION = "1.0";

    // Authentication validity period (24 hours)
    private static final long AUTH_VALIDITY_PERIOD = 24 * 60 * 60 * 1000;

    // Network timeouts
    private static final int CONNECT_TIMEOUT = 10000; // 10 seconds
    private static final int READ_TIMEOUT = 15000; // 15 seconds
    
    private static volatile BearModAuthManager instance;
    private final Context context;
    private final SharedPreferences prefs;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isAuthenticated = new AtomicBoolean(false);
    
    /**
     * Authentication callback interface
     */
    public interface AuthCallback {
        void onSuccess(String message);
        void onError(String error);
    }
    
    /**
     * Private constructor for singleton pattern
     */
    private BearModAuthManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newSingleThreadExecutor();
        
        // Check if previously authenticated
        checkPreviousAuthentication();
    }
    
    /**
     * Get singleton instance
     */
    public static BearModAuthManager getInstance(Context context) {
        if (instance == null) {
            synchronized (BearModAuthManager.class) {
                if (instance == null) {
                    instance = new BearModAuthManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * Initialize authentication manager
     */
    public void initialize(AuthCallback callback) {
        if (isInitialized.get()) {
            if (callback != null) {
                mainHandler.post(() -> callback.onSuccess("Authentication manager already initialized"));
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                // Simulate initialization
                Thread.sleep(500);
                
                isInitialized.set(true);
                Log.d(TAG, "Authentication manager initialized successfully");
                
                if (callback != null) {
                    mainHandler.post(() -> callback.onSuccess("Authentication manager initialized"));
                }
            } catch (Exception e) {
                Log.e(TAG, "Error initializing authentication manager", e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("Failed to initialize authentication"));
                }
            }
        });
    }
    
    /**
     * Authenticate with license key using KeyAuth API
     */
    public void authenticate(String licenseKey, AuthCallback callback) {
        if (!isInitialized.get()) {
            if (callback != null) {
                callback.onError("Authentication manager not initialized");
            }
            return;
        }

        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("License key cannot be empty");
            }
            return;
        }

        executor.execute(() -> {
            try {
                Log.d(TAG, "Authenticating with KeyAuth API");

                // Step 1: Initialize session with KeyAuth
                String sessionId = initializeKeyAuthSession();
                if (sessionId == null) {
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError("Failed to initialize authentication session"));
                    }
                    return;
                }

                // Step 2: Authenticate license key
                KeyAuthResponse authResponse = authenticateWithKeyAuth(sessionId, licenseKey);

                if (authResponse.success) {
                    // Save authentication state
                    saveAuthenticationState(licenseKey, sessionId, authResponse.userData);
                    isAuthenticated.set(true);

                    Log.d(TAG, "KeyAuth authentication successful");
                    if (callback != null) {
                        mainHandler.post(() -> callback.onSuccess("Authentication successful"));
                    }
                } else {
                    Log.w(TAG, "KeyAuth authentication failed: " + authResponse.message);
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError(authResponse.message));
                    }
                }

            } catch (Exception e) {
                Log.e(TAG, "Error during KeyAuth authentication", e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("Authentication error: " + e.getMessage()));
                }
            }
        });
    }
    
    /**
     * Check if user is currently authenticated
     */
    public boolean isAuthenticated() {
        return isAuthenticated.get() && isAuthenticationValid();
    }
    
    /**
     * Get stored license key
     */
    public String getLicenseKey() {
        return prefs.getString(KEY_LICENSE, "");
    }
    
    /**
     * Clear authentication state
     */
    public void logout() {
        isAuthenticated.set(false);
        prefs.edit()
                .remove(KEY_LICENSE)
                .remove(KEY_IS_AUTHENTICATED)
                .remove(KEY_LAST_AUTH_TIME)
                .apply();
        Log.d(TAG, "User logged out");
    }
    
    /**
     * KeyAuth Response class
     */
    private static class KeyAuthResponse {
        boolean success;
        String message;
        String userData;

        KeyAuthResponse(boolean success, String message, String userData) {
            this.success = success;
            this.message = message;
            this.userData = userData;
        }
    }

    /**
     * Initialize KeyAuth session
     */
    private String initializeKeyAuthSession() {
        try {
            URL url = new URL(KEYAUTH_API_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "BearMod-KeyAuth/1.0");

            // Create device HWID
            String hwid = generateDeviceHWID();

            // Prepare init request
            String postData = String.format(
                "type=init&name=%s&ownerid=%s&secret=%s&ver=%s&hash=%s",
                KEYAUTH_APP_NAME,
                KEYAUTH_OWNER_ID,
                KEYAUTH_APP_SECRET,
                KEYAUTH_VERSION,
                hwid
            );

            // Send request
            try (OutputStream os = connection.getOutputStream()) {
                os.write(postData.getBytes());
                os.flush();
            }

            // Read response
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    JSONObject jsonResponse = new JSONObject(response.toString());
                    if (jsonResponse.getBoolean("success")) {
                        String sessionId = jsonResponse.getString("sessionid");
                        Log.d(TAG, "KeyAuth session initialized successfully");
                        return sessionId;
                    } else {
                        Log.e(TAG, "KeyAuth init failed: " + jsonResponse.getString("message"));
                    }
                }
            } else {
                Log.e(TAG, "KeyAuth init HTTP error: " + responseCode);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error initializing KeyAuth session", e);
        }

        return null;
    }

    /**
     * Authenticate with KeyAuth API
     */
    private KeyAuthResponse authenticateWithKeyAuth(String sessionId, String licenseKey) {
        try {
            URL url = new URL(KEYAUTH_API_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "BearMod-KeyAuth/1.0");

            // Create device HWID
            String hwid = generateDeviceHWID();

            // Prepare license request
            String postData = String.format(
                "type=license&key=%s&hwid=%s&sessionid=%s",
                licenseKey,
                hwid,
                sessionId
            );

            // Send request
            try (OutputStream os = connection.getOutputStream()) {
                os.write(postData.getBytes());
                os.flush();
            }

            // Read response
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    JSONObject jsonResponse = new JSONObject(response.toString());
                    boolean success = jsonResponse.getBoolean("success");
                    String message = jsonResponse.optString("message", "Unknown error");
                    String userData = jsonResponse.optString("info", "");

                    return new KeyAuthResponse(success, message, userData);
                }
            } else {
                return new KeyAuthResponse(false, "HTTP error: " + responseCode, "");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error authenticating with KeyAuth", e);
            return new KeyAuthResponse(false, "Network error: " + e.getMessage(), "");
        }
    }

    /**
     * Generate device HWID for authentication
     */
    private String generateDeviceHWID() {
        try {
            StringBuilder hwid = new StringBuilder();

            // Android ID
            String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            if (androidId != null) {
                hwid.append(androidId);
            }

            // Device model and brand
            hwid.append(Build.MODEL);
            hwid.append(Build.BRAND);
            hwid.append(Build.DEVICE);

            // Create MD5 hash
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(hwid.toString().getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (Exception e) {
            Log.e(TAG, "Error generating device HWID", e);
            // Fallback to random UUID
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    /**
     * Save authentication state with session data
     */
    private void saveAuthenticationState(String licenseKey, String sessionId, String userData) {
        prefs.edit()
                .putString(KEY_LICENSE, licenseKey)
                .putString(KEY_SESSION_TOKEN, sessionId)
                .putString(KEY_USER_DATA, userData)
                .putBoolean(KEY_IS_AUTHENTICATED, true)
                .putLong(KEY_LAST_AUTH_TIME, System.currentTimeMillis())
                .apply();
    }
    
    /**
     * Check if authentication is still valid
     */
    private boolean isAuthenticationValid() {
        long lastAuthTime = prefs.getLong(KEY_LAST_AUTH_TIME, 0);
        long currentTime = System.currentTimeMillis();
        return (currentTime - lastAuthTime) < AUTH_VALIDITY_PERIOD;
    }
    
    /**
     * Check previous authentication state
     */
    private void checkPreviousAuthentication() {
        boolean wasAuthenticated = prefs.getBoolean(KEY_IS_AUTHENTICATED, false);
        if (wasAuthenticated && isAuthenticationValid()) {
            isAuthenticated.set(true);
            Log.d(TAG, "Previous authentication still valid");
        } else if (wasAuthenticated) {
            // Clear expired authentication
            logout();
            Log.d(TAG, "Previous authentication expired");
        }
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
