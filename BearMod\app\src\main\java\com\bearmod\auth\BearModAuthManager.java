package com.bearmod.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Simplified Authentication Manager for BearMod
 * Handles license validation and user authentication
 * Designed for stability and crash prevention
 */
public class BearModAuthManager {
    private static final String TAG = "BearModAuth";
    private static final String PREFS_NAME = "bearmod_auth";
    private static final String KEY_LICENSE = "license_key";
    private static final String KEY_IS_AUTHENTICATED = "is_authenticated";
    private static final String KEY_LAST_AUTH_TIME = "last_auth_time";
    
    // Authentication validity period (24 hours)
    private static final long AUTH_VALIDITY_PERIOD = 24 * 60 * 60 * 1000;
    
    private static volatile BearModAuthManager instance;
    private final Context context;
    private final SharedPreferences prefs;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isAuthenticated = new AtomicBoolean(false);
    
    /**
     * Authentication callback interface
     */
    public interface AuthCallback {
        void onSuccess(String message);
        void onError(String error);
    }
    
    /**
     * Private constructor for singleton pattern
     */
    private BearModAuthManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newSingleThreadExecutor();
        
        // Check if previously authenticated
        checkPreviousAuthentication();
    }
    
    /**
     * Get singleton instance
     */
    public static BearModAuthManager getInstance(Context context) {
        if (instance == null) {
            synchronized (BearModAuthManager.class) {
                if (instance == null) {
                    instance = new BearModAuthManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * Initialize authentication manager
     */
    public void initialize(AuthCallback callback) {
        if (isInitialized.get()) {
            if (callback != null) {
                mainHandler.post(() -> callback.onSuccess("Authentication manager already initialized"));
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                // Simulate initialization
                Thread.sleep(500);
                
                isInitialized.set(true);
                Log.d(TAG, "Authentication manager initialized successfully");
                
                if (callback != null) {
                    mainHandler.post(() -> callback.onSuccess("Authentication manager initialized"));
                }
            } catch (Exception e) {
                Log.e(TAG, "Error initializing authentication manager", e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("Failed to initialize authentication"));
                }
            }
        });
    }
    
    /**
     * Authenticate with license key
     */
    public void authenticate(String licenseKey, AuthCallback callback) {
        if (!isInitialized.get()) {
            if (callback != null) {
                callback.onError("Authentication manager not initialized");
            }
            return;
        }
        
        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("License key cannot be empty");
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                Log.d(TAG, "Authenticating with license key");
                
                // Simulate authentication process
                Thread.sleep(1000);
                
                // For now, accept any non-empty license key
                // In production, this would connect to KeyAuth API
                boolean authSuccess = validateLicenseFormat(licenseKey);
                
                if (authSuccess) {
                    // Save authentication state
                    saveAuthenticationState(licenseKey);
                    isAuthenticated.set(true);
                    
                    Log.d(TAG, "Authentication successful");
                    if (callback != null) {
                        mainHandler.post(() -> callback.onSuccess("Authentication successful"));
                    }
                } else {
                    Log.w(TAG, "Authentication failed - invalid license");
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError("Invalid license key"));
                    }
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error during authentication", e);
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("Authentication error: " + e.getMessage()));
                }
            }
        });
    }
    
    /**
     * Check if user is currently authenticated
     */
    public boolean isAuthenticated() {
        return isAuthenticated.get() && isAuthenticationValid();
    }
    
    /**
     * Get stored license key
     */
    public String getLicenseKey() {
        return prefs.getString(KEY_LICENSE, "");
    }
    
    /**
     * Clear authentication state
     */
    public void logout() {
        isAuthenticated.set(false);
        prefs.edit()
                .remove(KEY_LICENSE)
                .remove(KEY_IS_AUTHENTICATED)
                .remove(KEY_LAST_AUTH_TIME)
                .apply();
        Log.d(TAG, "User logged out");
    }
    
    /**
     * Validate license key format
     */
    private boolean validateLicenseFormat(String licenseKey) {
        // Basic validation - in production this would be more sophisticated
        return licenseKey != null && 
               licenseKey.trim().length() >= 8 && 
               !licenseKey.trim().equals("test") &&
               !licenseKey.trim().equals("demo");
    }
    
    /**
     * Save authentication state
     */
    private void saveAuthenticationState(String licenseKey) {
        prefs.edit()
                .putString(KEY_LICENSE, licenseKey)
                .putBoolean(KEY_IS_AUTHENTICATED, true)
                .putLong(KEY_LAST_AUTH_TIME, System.currentTimeMillis())
                .apply();
    }
    
    /**
     * Check if authentication is still valid
     */
    private boolean isAuthenticationValid() {
        long lastAuthTime = prefs.getLong(KEY_LAST_AUTH_TIME, 0);
        long currentTime = System.currentTimeMillis();
        return (currentTime - lastAuthTime) < AUTH_VALIDITY_PERIOD;
    }
    
    /**
     * Check previous authentication state
     */
    private void checkPreviousAuthentication() {
        boolean wasAuthenticated = prefs.getBoolean(KEY_IS_AUTHENTICATED, false);
        if (wasAuthenticated && isAuthenticationValid()) {
            isAuthenticated.set(true);
            Log.d(TAG, "Previous authentication still valid");
        } else if (wasAuthenticated) {
            // Clear expired authentication
            logout();
            Log.d(TAG, "Previous authentication expired");
        }
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
