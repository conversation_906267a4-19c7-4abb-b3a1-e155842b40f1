package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import androidx.test.core.app.ApplicationProvider;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Test class for BearModAuthManager to verify KeyAuth integration compatibility
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = Build.VERSION_CODES.O)
public class BearModAuthManagerTest {

    @Mock
    private Context mockContext;
    
    @Mock
    private SharedPreferences mockPrefs;
    
    @Mock
    private SharedPreferences.Editor mockEditor;

    private BearModAuthManager authManager;
    private Context realContext;
    private AutoCloseable mockitoCloseable;

    @Before
    public void setUp() {
        mockitoCloseable = MockitoAnnotations.openMocks(this);
        realContext = ApplicationProvider.getApplicationContext();
        
        // Setup mock preferences
        when(mockContext.getSharedPreferences(anyString(), eq(Context.MODE_PRIVATE)))
                .thenReturn(mockPrefs);
        when(mockPrefs.edit()).thenReturn(mockEditor);
        when(mockEditor.putString(anyString(), anyString())).thenReturn(mockEditor);
        when(mockEditor.putBoolean(anyString(), eq(true))).thenReturn(mockEditor);
        when(mockEditor.putLong(anyString(), eq(0L))).thenReturn(mockEditor);
        
        authManager = BearModAuthManager.getInstance(realContext);
    }

    @After
    public void tearDown() throws Exception {
        if (mockitoCloseable != null) {
            mockitoCloseable.close();
        }
    }

    @Test
    public void testKeyAuthConfigurationConstants() {
        // Test that KeyAuth configuration matches BearMod-Loader
        // These values should be accessible via reflection or public getters

        // Verify API URL is correct (updated to match current endpoint)
        String apiUrl = (String) getPrivateField("KEYAUTH_API_URL");
        assertTrue("API URL should use enc.mod-key.click domain",
                   apiUrl.contains("enc.mod-key.click"));
        assertTrue("API URL should use version 1.2",
                   apiUrl.contains("1.2"));
        
        // Verify app name matches BearMod-Loader
        assertEquals("App name should match BearMod-Loader", 
                    "com.bearmod.loader", getPrivateField("KEYAUTH_APP_NAME"));
        
        // Verify owner ID matches
        assertEquals("Owner ID should match BearMod-Loader", 
                    "yLoA9zcOEF", getPrivateField("KEYAUTH_OWNER_ID"));
        
        // Verify app version matches (updated for BearMod-Loader compatibility)
        assertEquals("App version should be 1.3",
                    "1.3", getPrivateField("KEYAUTH_APP_VERSION"));
    }

    @Test
    public void testAESKeyCompatibility() {
        // Verify AES key matches BearMod-Loader
        assertEquals("AES key must match BearMod-Loader exactly", 
                    "BearModLoader2024", getPrivateField("AES_KEY"));
    }

    @Test
    public void testAuthManagerInitialization() {
        // Test singleton pattern
        BearModAuthManager instance1 = BearModAuthManager.getInstance(realContext);
        BearModAuthManager instance2 = BearModAuthManager.getInstance(realContext);
        
        assertSame("Should return same instance", instance1, instance2);
        assertNotNull("Instance should not be null", instance1);
    }

    @Test
    public void testAuthenticationValidityPeriod() {
        // Verify authentication validity period is 24 hours
        long expectedPeriod = 24 * 60 * 60 * 1000L; // 24 hours in milliseconds
        Long actualPeriod = (Long) getPrivateField("AUTH_VALIDITY_PERIOD");
        assertEquals("Auth validity period should be 24 hours",
                    expectedPeriod, actualPeriod.longValue());
    }

    @Test
    public void testSharedPreferencesCompatibility() {
        // Verify shared preferences name matches BearMod-Loader
        assertEquals("Shared prefs name should match BearMod-Loader", 
                    "bearmod_shared", getPrivateField("SHARED_PREFS_NAME"));
    }

    @Test
    public void testBearTokenStructure() {
        // Test BearToken class structure
        BearModAuthManager.BearToken token = new BearModAuthManager.BearToken(
                "encrypted_payload", "signature", 1000L, 2000L, "device_id");
        
        assertEquals("encrypted_payload", token.encryptedPayload);
        assertEquals("signature", token.signature);
        assertEquals(1000L, token.createdTime);
        assertEquals(2000L, token.expiryTime);
        assertEquals("device_id", token.deviceId);
    }

    @Test
    public void testInitializationBasic() {
        // Test basic initialization without complex callback logic
        // This test verifies that the initialize method can be called without crashing

        // Test with null callback (should not crash)
        try {
            authManager.initialize(null);
            // If we get here without exception, the test passes
            assertTrue("Initialize with null callback should not crash", true);
        } catch (Exception e) {
            fail("Initialize with null callback should not throw exception: " + e.getMessage());
        }

        // Test with valid callback (should not crash)
        try {
            BearModAuthManager.AuthCallback callback = new BearModAuthManager.AuthCallback() {
                @Override
                public void onSuccess(String message) {
                    // Callback implementation - just for testing
                }

                @Override
                public void onError(String error) {
                    // Callback implementation - just for testing
                }
            };

            authManager.initialize(callback);
            // If we get here without exception, the test passes
            assertTrue("Initialize with valid callback should not crash", true);
        } catch (Exception e) {
            fail("Initialize with valid callback should not throw exception: " + e.getMessage());
        }
    }

    /**
     * Helper method to access private fields via reflection for testing
     */
    private Object getPrivateField(String fieldName) {
        try {
            java.lang.reflect.Field field = BearModAuthManager.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(null); // Static field
        } catch (Exception e) {
            fail("Could not access private field: " + fieldName + " - " + e.getMessage());
            return null;
        }
    }
}
