package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Test class for BearModAuthManager to verify KeyAuth integration compatibility
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = Build.VERSION_CODES.O)
public class BearModAuthManagerTest {

    @Mock
    private Context mockContext;
    
    @Mock
    private SharedPreferences mockPrefs;
    
    @Mock
    private SharedPreferences.Editor mockEditor;

    private BearModAuthManager authManager;
    private Context realContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        realContext = RuntimeEnvironment.application;
        
        // Setup mock preferences
        when(mockContext.getSharedPreferences(anyString(), eq(Context.MODE_PRIVATE)))
                .thenReturn(mockPrefs);
        when(mockPrefs.edit()).thenReturn(mockEditor);
        when(mockEditor.putString(anyString(), anyString())).thenReturn(mockEditor);
        when(mockEditor.putBoolean(anyString(), eq(true))).thenReturn(mockEditor);
        when(mockEditor.putLong(anyString(), eq(0L))).thenReturn(mockEditor);
        
        authManager = BearModAuthManager.getInstance(realContext);
    }

    @Test
    public void testKeyAuthConfigurationConstants() {
        // Test that KeyAuth configuration matches BearMod-Loader
        // These values should be accessible via reflection or public getters
        
        // Verify API URL is correct
        assertTrue("API URL should use version 1.3", 
                   getPrivateField("KEYAUTH_API_URL").contains("api/1.3"));
        
        // Verify app name matches BearMod-Loader
        assertEquals("App name should match BearMod-Loader", 
                    "com.bearmod.loader", getPrivateField("KEYAUTH_APP_NAME"));
        
        // Verify owner ID matches
        assertEquals("Owner ID should match BearMod-Loader", 
                    "yLoA9zcOEF", getPrivateField("KEYAUTH_OWNER_ID"));
        
        // Verify version matches
        assertEquals("Version should match BearMod-Loader", 
                    "1.3", getPrivateField("KEYAUTH_VERSION"));
        
        // Verify fixed hash is present
        assertEquals("App hash should match BearMod-Loader", 
                    "60885ac0cf1061079d5756a689630d13", getPrivateField("KEYAUTH_APP_HASH"));
    }

    @Test
    public void testAESKeyCompatibility() {
        // Verify AES key matches BearMod-Loader
        assertEquals("AES key must match BearMod-Loader exactly", 
                    "BearModLoader2024", getPrivateField("AES_KEY"));
    }

    @Test
    public void testAuthManagerInitialization() {
        // Test singleton pattern
        BearModAuthManager instance1 = BearModAuthManager.getInstance(realContext);
        BearModAuthManager instance2 = BearModAuthManager.getInstance(realContext);
        
        assertSame("Should return same instance", instance1, instance2);
        assertNotNull("Instance should not be null", instance1);
    }

    @Test
    public void testAuthenticationValidityPeriod() {
        // Verify authentication validity period is 24 hours
        long expectedPeriod = 24 * 60 * 60 * 1000L; // 24 hours in milliseconds
        assertEquals("Auth validity period should be 24 hours", 
                    expectedPeriod, (Long) getPrivateField("AUTH_VALIDITY_PERIOD"));
    }

    @Test
    public void testSharedPreferencesCompatibility() {
        // Verify shared preferences name matches BearMod-Loader
        assertEquals("Shared prefs name should match BearMod-Loader", 
                    "bearmod_shared", getPrivateField("SHARED_PREFS_NAME"));
    }

    @Test
    public void testBearTokenStructure() {
        // Test BearToken class structure
        BearModAuthManager.BearToken token = new BearModAuthManager.BearToken(
                "encrypted_payload", "signature", 1000L, 2000L, "device_id");
        
        assertEquals("encrypted_payload", token.encryptedPayload);
        assertEquals("signature", token.signature);
        assertEquals(1000L, token.createdTime);
        assertEquals(2000L, token.expiryTime);
        assertEquals("device_id", token.deviceId);
    }

    @Test
    public void testInitializationCallback() {
        // Test initialization with callback
        final boolean[] callbackCalled = {false};
        final String[] callbackMessage = {null};
        
        BearModAuthManager.AuthCallback callback = new BearModAuthManager.AuthCallback() {
            @Override
            public void onSuccess(String message) {
                callbackCalled[0] = true;
                callbackMessage[0] = message;
            }

            @Override
            public void onError(String error) {
                callbackCalled[0] = true;
                callbackMessage[0] = error;
            }
        };
        
        authManager.initialize(callback);
        
        // Wait a bit for async operation
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        assertTrue("Callback should be called", callbackCalled[0]);
        assertNotNull("Callback message should not be null", callbackMessage[0]);
    }

    /**
     * Helper method to access private fields via reflection for testing
     */
    private Object getPrivateField(String fieldName) {
        try {
            java.lang.reflect.Field field = BearModAuthManager.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(null); // Static field
        } catch (Exception e) {
            fail("Could not access private field: " + fieldName + " - " + e.getMessage());
            return null;
        }
    }
}
