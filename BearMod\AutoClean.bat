@echo off

:: AutoClean_Upgraded.bat - Full Environment Fixer for BearMod Build with Logging and Emulator Check

:: Section 0: Change directory to BearMod project root
cd /d D:\BearMod-Project\BearMod

:: Set log file path
set LOGFILE=D:\BearMod-Project\BearMod\build_log.txt
if exist %LOGFILE% del %LOGFILE%

:: Section 1: Set Environment Variables (User level only to avoid access errors)
set TEMP=D:\Temp
set TMP=D:\Temp
set GRADLE_USER_HOME=D:\AndroidBuildEnv\.gradle_cache
set ANDROID_AVD_HOME=D:\AndroidBuildEnv\.android\avd

:: Section 2: Verify Cache Paths
mkdir D:\AndroidBuildEnv\.gradle_cache 2>> %LOGFILE%
if exist "%USERPROFILE%\.gradle" (
    xcopy /E /I /H /Y "%USERPROFILE%\.gradle\*" "D:\AndroidBuildEnv\.gradle_cache\" >> %LOGFILE%
)

:: Section 3: Create Gradle Properties
(
    echo org.gradle.daemon=true
    echo org.gradle.jvmargs=-Xmx2048m
    echo org.gradle.caching=true
    echo org.gradle.parallel=true
    echo org.gradle.configureondemand=true
    echo org.gradle.projectCacheDir=D:\\AndroidBuildEnv\\.gradle_cache
    echo org.gradle.vfs.watch=false
    echo android.useAndroidX=true
    echo android.enableJetifier=true
) > gradle.properties

:: Section 4: Create local.properties
(
    echo ndk.dir=D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006
    echo sdk.dir=D:\\AndroidBuildEnv\\SDK
) > local.properties

:: Section 5: Kill Stale Processes
for %%x in (java.exe adb.exe clang++.exe code.exe) do taskkill /f /im %%x 2>> %LOGFILE%

:: Section 6: Build Cleanup and Diagnostic Build
call gradlew --stop >> %LOGFILE% 2>&1
call gradlew clean build --no-daemon --stacktrace >> %LOGFILE% 2>&1

:: Section 7: Emulator Check and Launch
D:\AndroidBuildEnv\SDK\emulator\emulator.exe -list-avds > tmp_avds.txt
findstr /R /C:"." tmp_avds.txt >nul
if %errorlevel%==0 (
    set /p AVD_NAME=<tmp_avds.txt
    echo Launching emulator %AVD_NAME% >> %LOGFILE%
    start "" D:\AndroidBuildEnv\SDK\emulator\emulator.exe -avd %AVD_NAME%
) else (
    echo No AVDs found. You may need to create one using avdmanager. >> %LOGFILE%
)
del tmp_avds.txt

:: Display summary or error hint
findstr /C:"FAILURE:" /C:"error" %LOGFILE%

pause
