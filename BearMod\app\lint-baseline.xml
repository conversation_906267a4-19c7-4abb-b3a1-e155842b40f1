<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.10.1)" variant="all" version="8.10.1">

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String lowerName = fileName.toLowerCase();"
        errorLine2="                                    ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="443"
            column="37"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                if (onePlusProp != null &amp;&amp; onePlusProp.toLowerCase().contains(&quot;hydrogen&quot;)) {"
        errorLine2="                                                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="1244"
            column="56"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                if (rogProp != null &amp;&amp; rogProp.toLowerCase().contains(&quot;CN_Phone&quot;)) {"
        errorLine2="                                               ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="1249"
            column="48"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            if (sVersion.toUpperCase().contains(ROM_FLYME)) {"
        errorLine2="                         ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="1258"
            column="26"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                return Build.MANUFACTURER.toUpperCase();"
        errorLine2="                                          ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="1261"
            column="43"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            if (onePlusProp != null &amp;&amp; onePlusProp.toLowerCase().contains(&quot;hydrogen&quot;)) {"
        errorLine2="                                                   ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="2099"
            column="52"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            if (rogProp != null &amp;&amp; rogProp.toLowerCase().contains(&quot;CN_Phone&quot;)) {"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="2104"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            if (sVersion.toUpperCase().contains(ROM_FLYME)) {"
        errorLine2="                         ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="2111"
            column="26"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                currentROM = Build.MANUFACTURER.toUpperCase();"
        errorLine2="                                                ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/RecorderFakeUtils.java"
            line="2114"
            column="49"/>
    </issue>

    <issue
        id="InlinedApi"
        message="Field requires API level 31 (current min is 30): `android.content.Context#VIBRATOR_MANAGER_SERVICE`"
        errorLine1="                        VibratorManager vibratorManager = (VibratorManager) getSystemService(Context.VIBRATOR_MANAGER_SERVICE);"
        errorLine2="                                                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="2972"
            column="94"/>
    </issue>

    <issue
        id="NewApi"
        message="Class requires API level 31 (current min is 30): `android.os.VibratorManager`"
        errorLine1="                        VibratorManager vibratorManager = (VibratorManager) getSystemService(Context.VIBRATOR_MANAGER_SERVICE);"
        errorLine2="                                                           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="2972"
            column="60"/>
    </issue>

    <issue
        id="NewApi"
        message="Call requires API level 31 (current min is 30): `android.os.VibratorManager#getDefaultVibrator`"
        errorLine1="                        vibratorManager.getDefaultVibrator().vibrate(vibrationEffect);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="2974"
            column="41"/>
    </issue>

    <issue
        id="SuspiciousIndentation"
        message="The indentation string here is different from on the previous line (`&quot; &quot;` vs `\t`)"
        errorLine1="       Floating.DrawOn(this, canvas);"
        errorLine2="~~~~~~~">
        <location
            file="src/main/java/com/bearmod/ESPView.java"
            line="86"
            column="1"/>
        <location
            file="src/main/java/com/bearmod/ESPView.java"
            line="85"
            column="1"
            message="Previous line indentation here"/>
    </issue>

    <issue
        id="SuspiciousIndentation"
        message="The indentation string here is different from on the previous line (`&quot; &quot;` vs `\t`)"
        errorLine1="  canvas.drawARGB(0, 0, 0, 0);"
        errorLine2="~~">
        <location
            file="src/main/java/com/bearmod/FileUtil.java"
            line="575"
            column="1"/>
        <location
            file="src/main/java/com/bearmod/FileUtil.java"
            line="574"
            column="1"
            message="Previous line indentation here"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.squareup.retrofit2:retrofit than 2.11.0 is available: 3.0.0"
        errorLine1="    implementation &apos;com.squareup.retrofit2:retrofit:2.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="64"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.squareup.retrofit2:converter-gson than 2.11.0 is available: 3.0.0"
        errorLine1="    implementation &apos;com.squareup.retrofit2:converter-gson:2.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="65"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 30"
        errorLine1="        if (Build.VERSION.SDK_INT >= 30) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="291"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 30"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="710"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 30. Merge all the resources in this folder into `drawable`.">
        <location
            file="src/main/res/drawable-v24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="This folder configuration (`v26`) is unnecessary; `minSdkVersion` is 30. Merge all the resources in this folder into `mipmap-anydpi`.">
        <location
            file="src/main/res/mipmap-anydpi-v26"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields; this is a memory leak"
        errorLine1="    public static RelativeLayout iconLayout;"
        errorLine2="           ~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="138"
            column="12"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields; this is a memory leak"
        errorLine1="    public static LinearLayout mainLayout, bodyLayout;"
        errorLine2="           ~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="139"
            column="12"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields; this is a memory leak"
        errorLine1="    public static LinearLayout mainLayout, bodyLayout;"
        errorLine2="           ~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="139"
            column="12"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields; this is a memory leak"
        errorLine1="    public static Context g_context;"
        errorLine2="           ~~~~~~">
        <location
            file="src/main/java/com/bearmod/Floating.java"
            line="247"
            column="12"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `LanguageManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static volatile LanguageManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/LanguageManager.java"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#FF000000` with a theme that also paints a background (inferred theme is `@style/Theme_BearMod`)"
        errorLine1="        android:background=&quot;#FF000000&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="8"
            column="9"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.activity_main` appears to be unused"
        errorLine1="&lt;LinearLayout"
        errorLine2="^">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.colorPrimary` appears to be unused"
        errorLine1="    &lt;color name=&quot;colorPrimary&quot;>#008577&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.colorPrimaryDark` appears to be unused"
        errorLine1="    &lt;color name=&quot;colorPrimaryDark&quot;>#00574B&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.colorAccent` appears to be unused"
        errorLine1="    &lt;color name=&quot;colorAccent&quot;>#D81B60&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.home_yellow` appears to be unused"
        errorLine1="    &lt;color name=&quot;home_yellow&quot;>#E8D655&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.esp_line` appears to be unused"
        errorLine1="        &lt;string name=&quot;esp_line&quot;>Line&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="3"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.esp_bone` appears to be unused"
        errorLine1="        &lt;string name=&quot;esp_bone&quot;>Bone&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.esp_info` appears to be unused"
        errorLine1="        &lt;string name=&quot;esp_info&quot;>Info&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="5"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.esp_weapon` appears to be unused"
        errorLine1="        &lt;string name=&quot;esp_weapon&quot;>Weapon&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="6"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.esp_grenade` appears to be unused"
        errorLine1="        &lt;string name=&quot;esp_grenade&quot;>Grenade Warning&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.aim_aimbot` appears to be unused"
        errorLine1="        &lt;string name=&quot;aim_aimbot&quot;>AimBot&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="8"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.aim_ignore` appears to be unused"
        errorLine1="        &lt;string name=&quot;aim_ignore&quot;>IgnoreBot&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.aim_knocked` appears to be unused"
        errorLine1="        &lt;string name=&quot;aim_knocked&quot;>Aim-Knocked&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.skin_enable` appears to be unused"
        errorLine1="        &lt;string name=&quot;skin_enable&quot;>Skin-Enable&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.deadbox` appears to be unused"
        errorLine1="        &lt;string name=&quot;deadbox&quot;>DeadBox (Ingame for open)&lt;/string>"
        errorLine2="                ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="17"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.FloatingServiceTheme` appears to be unused"
        errorLine1="    &lt;style name=&quot;FloatingServiceTheme&quot; parent=&quot;Theme.MaterialComponents.DayNight.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.AppTheme` appears to be unused"
        errorLine1="    &lt;style name=&quot;AppTheme&quot; parent=&quot;Theme.MaterialComponents.DayNight.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive icon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive roundIcon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Hello, World!&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Hello, World!&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="14"
            column="13"/>
    </issue>

</issues>
