package com.bearmod.api;

import org.json.JSONObject;

/**
 * KeyAuth User representation
 * Compatible with BearMod-Loader KeyAuth implementation
 */
public class KeyAuthUser {
    private final String username;
    private final boolean authenticated;
    private final JSONObject userInfo;

    public KeyAuthUser(String username, boolean authenticated) {
        this.username = username;
        this.authenticated = authenticated;
        this.userInfo = null;
    }

    public KeyAuthUser(String username, boolean authenticated, JSONObject userInfo) {
        this.username = username;
        this.authenticated = authenticated;
        this.userInfo = userInfo;
    }

    public String getUsername() {
        return username;
    }

    public boolean isAuthenticated() {
        return authenticated;
    }

    public JSONObject getUserInfo() {
        return userInfo;
    }

    public String getExpiryDate() {
        if (userInfo != null) {
            return userInfo.optString("expiry", null);
        }
        return null;
    }

    public String getRegistrationDate() {
        if (userInfo != null) {
            return userInfo.optString("createdate", null);
        }
        return null;
    }
}
