package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import android.os.Build;
import android.provider.Settings;

import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;

/**
 * Authentication Recovery Utility
 * Helps recover from authentication issues after app reinstallation
 */
public class AuthRecoveryUtil {
    
    private static final String TAG = "AuthRecovery";
    
    // Preference names that need to be cleared
    private static final String[] PREF_NAMES = {
        "bearmod_auth",
        "bearmod_shared", 
        "bear_token_prefs",
        "keyauth_prefs"
    };
    
    /**
     * Complete authentication data cleanup
     * Call this when experiencing authentication issues after reinstall
     */
    public static void performCompleteAuthCleanup(Context context) {
        Log.d(TAG, "Starting complete authentication cleanup");
        
        try {
            // Clear all authentication-related shared preferences
            for (String prefName : PREF_NAMES) {
                SharedPreferences prefs = context.getSharedPreferences(prefName, Context.MODE_PRIVATE);
                prefs.edit().clear().apply();
                Log.d(TAG, "Cleared preferences: " + prefName);
            }
            
            // Clear any cached authentication files
            clearAuthenticationFiles(context);
            
            // Reset singleton instances (if any)
            resetSingletonInstances();
            
            Log.d(TAG, "Complete authentication cleanup finished");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during authentication cleanup", e);
        }
    }
    
    /**
     * Verify device HWID consistency
     * Helps diagnose HWID-related authentication issues
     */
    public static String verifyDeviceHWID(Context context) {
        try {
            Log.d(TAG, "Verifying device HWID generation");
            
            // Generate HWID using same method as BearModAuthManager
            String serial;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (context.checkSelfPermission(android.Manifest.permission.READ_PHONE_STATE)
                    == android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    serial = Build.getSerial();
                } else {
                    serial = Settings.Secure.getString(context.getContentResolver(),
                                                     Settings.Secure.ANDROID_ID);
                }
            } else {
                // For API < 26, use Android ID as fallback instead of deprecated Build.SERIAL
                serial = Settings.Secure.getString(context.getContentResolver(),
                                                  Settings.Secure.ANDROID_ID);
            }
            
            // Combine identifiers
            String combined = serial + "-" + Build.FINGERPRINT;
            
            // Hash the combined string
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(combined.getBytes(StandardCharsets.UTF_8));
            
            // Convert to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            String hwid = hexString.toString().substring(0, 32);
            Log.d(TAG, "Generated HWID: " + hwid);
            return hwid;
            
        } catch (Exception e) {
            Log.e(TAG, "Error generating HWID", e);
            return "hwid_generation_failed";
        }
    }
    
    /**
     * Test KeyAuth connectivity
     * Verifies if KeyAuth API is accessible
     */
    public static void testKeyAuthConnectivity(Context context, ConnectivityCallback callback) {
        new Thread(() -> {
            try {
                Log.d(TAG, "Testing KeyAuth connectivity");
                
                java.net.URL url = new java.net.URL("https://keyauth.win/api/1.3/");
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                int responseCode = connection.getResponseCode();
                
                if (responseCode == 200) {
                    Log.d(TAG, "KeyAuth API is accessible");
                    if (callback != null) {
                        callback.onSuccess("KeyAuth API is accessible");
                    }
                } else {
                    Log.w(TAG, "KeyAuth API returned code: " + responseCode);
                    if (callback != null) {
                        callback.onError("KeyAuth API returned code: " + responseCode);
                    }
                }
                
            } catch (Exception e) {
                Log.e(TAG, "KeyAuth connectivity test failed", e);
                if (callback != null) {
                    callback.onError("Connectivity test failed: " + e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * Validate authentication configuration
     * Checks if all required configuration is present
     */
    public static boolean validateAuthConfiguration() {
        try {
            Log.d(TAG, "Validating authentication configuration");
            
            // Check if BearModAuthManager class exists and has required constants
            Class<?> authClass = Class.forName("com.bearmod.loader.auth.BearModAuthManager");
            
            // Verify required fields exist (using reflection)
            java.lang.reflect.Field apiUrlField = authClass.getDeclaredField("KEYAUTH_API_URL");
            java.lang.reflect.Field appNameField = authClass.getDeclaredField("KEYAUTH_APP_NAME");
            java.lang.reflect.Field ownerIdField = authClass.getDeclaredField("KEYAUTH_OWNER_ID");
            java.lang.reflect.Field appSecretField = authClass.getDeclaredField("KEYAUTH_APP_SECRET");
            java.lang.reflect.Field appHashField = authClass.getDeclaredField("KEYAUTH_APP_HASH");
            
            apiUrlField.setAccessible(true);
            appNameField.setAccessible(true);
            ownerIdField.setAccessible(true);
            appSecretField.setAccessible(true);
            appHashField.setAccessible(true);
            
            String apiUrl = (String) apiUrlField.get(null);
            String appName = (String) appNameField.get(null);
            String ownerId = (String) ownerIdField.get(null);
            String appSecret = (String) appSecretField.get(null);
            String appHash = (String) appHashField.get(null);
            
            // Validate configuration values
            boolean isValid = apiUrl != null && apiUrl.contains("keyauth.win/api/1.3") &&
                             appName != null && appName.equals("com.bearmod.loader") &&
                             ownerId != null && ownerId.equals("yLoA9zcOEF") &&
                             appSecret != null && !appSecret.isEmpty() &&
                             appHash != null && appHash.equals("60885ac0cf1061079d5756a689630d13");
            
            Log.d(TAG, "Configuration validation result: " + isValid);
            return isValid;
            
        } catch (Exception e) {
            Log.e(TAG, "Configuration validation failed", e);
            return false;
        }
    }
    
    /**
     * Generate diagnostic report
     * Creates a comprehensive report of authentication status
     */
    public static String generateDiagnosticReport(Context context) {
        StringBuilder report = new StringBuilder();
        report.append("=== BearMod Authentication Diagnostic Report ===\n");
        report.append("Timestamp: ").append(new java.util.Date()).append("\n\n");
        
        // Device information
        report.append("Device Information:\n");
        report.append("- Android Version: ").append(Build.VERSION.RELEASE).append("\n");
        report.append("- API Level: ").append(Build.VERSION.SDK_INT).append("\n");
        report.append("- Device Model: ").append(Build.MODEL).append("\n");
        report.append("- Device Fingerprint: ").append(Build.FINGERPRINT).append("\n\n");
        
        // HWID verification
        report.append("HWID Verification:\n");
        String hwid = verifyDeviceHWID(context);
        report.append("- Generated HWID: ").append(hwid).append("\n\n");
        
        // Configuration validation
        report.append("Configuration Validation:\n");
        boolean configValid = validateAuthConfiguration();
        report.append("- Configuration Valid: ").append(configValid).append("\n\n");
        
        // Preferences status
        report.append("Preferences Status:\n");
        for (String prefName : PREF_NAMES) {
            SharedPreferences prefs = context.getSharedPreferences(prefName, Context.MODE_PRIVATE);
            int keyCount = prefs.getAll().size();
            report.append("- ").append(prefName).append(": ").append(keyCount).append(" keys\n");
        }
        
        report.append("\n=== End of Report ===");
        
        Log.d(TAG, "Diagnostic report generated");
        return report.toString();
    }
    
    /**
     * Clear authentication files from internal storage
     */
    private static void clearAuthenticationFiles(Context context) {
        try {
            // Clear any authentication-related files
            java.io.File internalDir = context.getFilesDir();
            java.io.File[] files = internalDir.listFiles();
            
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.getName().contains("auth") || 
                        file.getName().contains("token") ||
                        file.getName().contains("keyauth")) {
                        if (file.delete()) {
                            Log.d(TAG, "Deleted auth file: " + file.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing authentication files", e);
        }
    }
    
    /**
     * Reset singleton instances (if any exist)
     */
    private static void resetSingletonInstances() {
        try {
            // Reset BearModAuthManager singleton if it exists
            Class<?> authClass = Class.forName("com.bearmod.loader.auth.BearModAuthManager");
            java.lang.reflect.Field instanceField = authClass.getDeclaredField("instance");
            instanceField.setAccessible(true);
            instanceField.set(null, null);
            
            Log.d(TAG, "Reset BearModAuthManager singleton");
            
        } catch (Exception e) {
            Log.d(TAG, "No singleton to reset or reset failed: " + e.getMessage());
        }
    }
    
    /**
     * Callback interface for connectivity testing
     */
    public interface ConnectivityCallback {
        void onSuccess(String message);
        void onError(String error);
    }
}
