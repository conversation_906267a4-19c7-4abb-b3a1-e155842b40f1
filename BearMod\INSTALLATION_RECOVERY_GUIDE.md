# BearMod Installation Recovery Guide

## Issue Analysis
**Error Code -25**: "Can't downgrade apps" - This occurs when trying to install an APK with a lower `versionCode` than the previously installed version.

## Root Causes Identified
1. **Version Downgrade Conflict**: Previous installation had higher `versionCode`
2. **Signature Mismatch**: Different signing keys between builds
3. **Package Cache Issues**: Android package manager cache corruption
4. **Authentication Token Corruption**: Previous auth data may be corrupted

## 🔧 IMMEDIATE RECOVERY STEPS

### Step 1: Complete App Removal
```bash
# Remove all traces of BearMod
adb uninstall com.bearmod.loader
adb shell pm clear com.bearmod.loader
adb shell rm -rf /data/data/com.bearmod.loader
adb shell rm -rf /sdcard/Android/data/com.bearmod.loader
```

### Step 2: Clear System Cache
```bash
# Clear package manager cache
adb shell pm clear com.android.packageinstaller
adb shell pm clear com.google.android.packageinstaller

# Clear system cache (if rooted)
adb shell rm -rf /data/system/packages.xml.bak
```

### Step 3: Clean Build and Reinstall
```bash
# Navigate to BearMod project directory
cd /path/to/BearMod-Project/BearMod

# Clean previous builds
./gradlew clean

# Build new APK with increased version code
./gradlew assembleDebug

# Install fresh APK
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

## 📱 MANUAL DEVICE RECOVERY

### Option A: Settings Method
1. **Go to Settings** → Apps → BearMod
2. **Force Stop** the app
3. **Clear Storage** and **Clear Cache**
4. **Uninstall** the app completely
5. **Restart** your device
6. **Install** the new APK

### Option B: Recovery Mode (if needed)
1. **Boot into Recovery Mode**
2. **Wipe Cache Partition**
3. **Reboot** to system
4. **Install** new APK

## 🔐 AUTHENTICATION RECOVERY

### KeyAuth Integration Verification
The updated BearModAuthManager.java includes:
- ✅ Correct KeyAuth API configuration (v1.3)
- ✅ BearMod-Loader compatible HWID generation
- ✅ AES encryption for token security
- ✅ Native security manager integration

### Authentication Data Cleanup
```java
// Add this method to BearModAuthManager.java for recovery
public void clearAllAuthenticationData() {
    try {
        // Clear local preferences
        prefs.edit().clear().apply();
        
        // Clear shared preferences
        sharedPrefs.edit().clear().apply();
        
        // Clear any cached tokens
        context.getSharedPreferences("bearmod_shared", Context.MODE_PRIVATE)
               .edit().clear().apply();
        
        // Reset authentication state
        isAuthenticated.set(false);
        isInitialized.set(false);
        
        Log.d(TAG, "All authentication data cleared for recovery");
    } catch (Exception e) {
        Log.e(TAG, "Error clearing authentication data", e);
    }
}
```

## 🧪 TESTING AUTHENTICATION WORKFLOW

### Step 1: Initialize Authentication Manager
```java
BearModAuthManager authManager = BearModAuthManager.getInstance(context);
authManager.initialize(new BearModAuthManager.AuthCallback() {
    @Override
    public void onSuccess(String message) {
        Log.d("Auth", "Initialization successful: " + message);
        // Proceed to license validation
    }
    
    @Override
    public void onError(String error) {
        Log.e("Auth", "Initialization failed: " + error);
        // Handle initialization error
    }
});
```

### Step 2: Test License Validation
```java
authManager.authenticate("YOUR_LICENSE_KEY", new BearModAuthManager.AuthCallback() {
    @Override
    public void onSuccess(String message) {
        Log.d("Auth", "Authentication successful: " + message);
        // Verify token generation
        testTokenGeneration();
    }
    
    @Override
    public void onError(String error) {
        Log.e("Auth", "Authentication failed: " + error);
        // Handle authentication error
    }
});
```

### Step 3: Verify Token Compatibility
```java
private void testTokenGeneration() {
    // Check if BearToken was generated and stored
    SharedPreferences sharedPrefs = context.getSharedPreferences("bearmod_shared", Context.MODE_PRIVATE);
    
    String token = sharedPrefs.getString("bear_token", null);
    String signature = sharedPrefs.getString("bear_signature", null);
    long expiry = sharedPrefs.getLong("bear_expiry", 0);
    
    if (token != null && signature != null && expiry > System.currentTimeMillis()) {
        Log.d("Auth", "BearToken generated successfully and is valid");
        Log.d("Auth", "Token expires in: " + (expiry - System.currentTimeMillis()) + "ms");
    } else {
        Log.e("Auth", "BearToken generation failed or token is invalid");
    }
}
```

## 🔍 TROUBLESHOOTING SPECIFIC ISSUES

### Issue: "Authentication manager not initialized"
**Solution**: Ensure `initialize()` is called before `authenticate()`

### Issue: "Failed to initialize authentication session"
**Solution**: 
1. Check internet connectivity
2. Verify KeyAuth API configuration
3. Check if KeyAuth dashboard is blocking requests

### Issue: "Loader signature verification failed"
**Solution**:
1. Ensure NativeSecurityManager is properly integrated
2. Check if native libraries are loaded correctly
3. Verify app signing configuration

### Issue: "Device HWID mismatch"
**Solution**:
1. Clear authentication data completely
2. Regenerate device fingerprint
3. Re-authenticate with license key

## 📋 BUILD CONFIGURATION FIXES

### Updated build.gradle Changes:
```gradle
defaultConfig {
    applicationId = "com.bearmod.loader"
    minSdk = 29
    targetSdk = 35
    versionCode = 100  // ← INCREASED to avoid downgrade issues
    versionName = "3.8.0"
    // ... rest of config
}
```

### Signing Configuration:
```gradle
signingConfigs {
    debug {
        storeFile file('debug.keystore')
        storePassword 'android'
        keyAlias 'androiddebugkey'
        keyPassword 'android'
    }
}
```

## ✅ VERIFICATION CHECKLIST

After following recovery steps, verify:

- [ ] App installs without error -25
- [ ] Authentication manager initializes successfully
- [ ] License key validation works with existing keys
- [ ] BearToken generation completes without errors
- [ ] Token is stored in shared preferences correctly
- [ ] Device HWID generation is consistent
- [ ] Native security manager integration works
- [ ] No authentication errors in logs
- [ ] App doesn't crash during auth workflow
- [ ] BearMod-Loader compatibility maintained

## 🚨 EMERGENCY RECOVERY

If all else fails:
1. **Factory reset** authentication data
2. **Use different device** for testing
3. **Contact KeyAuth support** for dashboard issues
4. **Check BearMod-Loader** for reference implementation

## 📞 SUPPORT INFORMATION

- **KeyAuth Dashboard**: Check hash configuration `60885ac0cf1061079d5756a689630d13`
- **License Issues**: Verify license key is active and not expired
- **Technical Issues**: Check logs for specific error messages
- **Compatibility**: Ensure BearMod-Loader is working correctly

## 🔧 AUTOMATED RECOVERY

### Quick Recovery Script
Run the automated recovery script:
```bash
# Windows
recovery_script.bat

# Manual ADB commands
adb uninstall com.bearmod.loader
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

## 📊 CHANGES MADE

### 1. Version Code Fix
- **Updated**: `versionCode = 100` (was 1)
- **Reason**: Prevents downgrade error -25
- **Location**: `app/build.gradle`

### 2. Native Security Manager
- **Created**: Fallback implementation for missing native components
- **Features**: Java-based signature verification
- **Location**: `app/src/main/java/com/bearmod/loader/security/NativeSecurityManager.java`

### 3. Authentication Recovery Utility
- **Created**: `AuthRecoveryUtil.java` for debugging auth issues
- **Features**: Complete auth cleanup, HWID verification, connectivity testing
- **Usage**: Call `AuthRecoveryUtil.performCompleteAuthCleanup(context)` if needed

### 4. Build Success
- ✅ **Compilation**: Fixed all compilation errors
- ✅ **APK Generated**: `app/build/outputs/apk/debug/app-debug.apk`
- ✅ **Version**: 3.8.0 (versionCode 100)

## 🚀 IMMEDIATE ACTION PLAN

### For Your Current Issue:
1. **Run recovery script**: `recovery_script.bat`
2. **Install new APK**: Should install without error -25
3. **Test authentication**: Use existing license key
4. **Monitor logs**: `adb logcat -s BearModAuth`

### If Authentication Fails:
```java
// Add to your MainActivity or test activity
AuthRecoveryUtil.performCompleteAuthCleanup(this);
String diagnosticReport = AuthRecoveryUtil.generateDiagnosticReport(this);
Log.d("Recovery", diagnosticReport);
```

This recovery guide should resolve the installation error and ensure proper authentication functionality after reinstallation.
