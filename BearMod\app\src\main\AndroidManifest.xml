<?xml version='1.0' encoding='utf-8'?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- view network connections -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <!-- have full network access -->
    <uses-permission android:name="android.permission.INTERNET"/>

    <!-- This app can appear on top of other apps -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>

    <!-- read phone status and identity -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>

    <!-- allow WLAN Multicast reception -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE"/>

    <!-- connect and disconnect from WLAN -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.VIBRATE"/>

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage"/>

    <application
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BearMod"
        android:requestLegacyExternalStorage="true"
        tools:ignore="RtlEnabled">

        <activity
            android:name="com.bearmod.loader.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.BearMod"
            android:screenOrientation="sensorLandscape"
            tools:ignore="DiscouragedApi,IntentFilterExportedReceiver">

            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <service
            android:name="com.bearmod.loader.Floating"
            android:enabled="true"
            android:exported="false" />
    </application>
</manifest>