package com.bearmod.loader;

import android.app.Activity;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.ScrollView;
import android.widget.LinearLayout;

import com.bearmod.loader.auth.BearModAuthManager;
import com.bearmod.loader.auth.AuthenticationTester;
import com.bearmod.loader.auth.AuthRecoveryUtil;

/**
 * Authentication Test Activity
 * Use this activity to test authentication after installation
 */
public class AuthTestActivity extends Activity {
    
    private static final String TAG = "AuthTestActivity";
    
    private EditText licenseKeyInput;
    private Button testButton;
    private Button recoveryButton;
    private Button diagnosticsButton;
    private TextView statusText;
    private TextView logText;
    private ScrollView logScrollView;
    
    private BearModAuthManager authManager;
    private StringBuilder logBuilder = new StringBuilder();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Create UI programmatically (no XML needed)
        createUI();
        
        // Initialize authentication manager
        authManager = BearModAuthManager.getInstance(this);
        
        // Log initial status
        appendLog("=== BearMod Authentication Test ===");
        appendLog("App Version: 3.8.0 (versionCode 100)");
        appendLog("Ready for testing...");
    }
    
    private void createUI() {
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(20, 20, 20, 20);
        
        // License key input
        licenseKeyInput = new EditText(this);
        licenseKeyInput.setHint("Enter your license key");
        licenseKeyInput.setSingleLine(true);
        layout.addView(licenseKeyInput);
        
        // Test button
        testButton = new Button(this);
        testButton.setText("Run Authentication Test");
        testButton.setOnClickListener(v -> runAuthenticationTest());
        layout.addView(testButton);
        
        // Recovery button
        recoveryButton = new Button(this);
        recoveryButton.setText("Run Recovery Cleanup");
        recoveryButton.setOnClickListener(v -> runRecoveryCleanup());
        layout.addView(recoveryButton);
        
        // Diagnostics button
        diagnosticsButton = new Button(this);
        diagnosticsButton.setText("Generate Diagnostics");
        diagnosticsButton.setOnClickListener(v -> generateDiagnostics());
        layout.addView(diagnosticsButton);
        
        // Status text
        statusText = new TextView(this);
        statusText.setText("Status: Ready");
        statusText.setTextSize(16);
        layout.addView(statusText);
        
        // Log text in scroll view
        logText = new TextView(this);
        logText.setTextSize(12);
        logText.setTextIsSelectable(true);
        
        logScrollView = new ScrollView(this);
        logScrollView.addView(logText);
        logScrollView.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 0, 1.0f));
        layout.addView(logScrollView);
        
        setContentView(layout);
    }
    
    private void runAuthenticationTest() {
        String licenseKey = licenseKeyInput.getText().toString().trim();
        
        if (licenseKey.isEmpty()) {
            appendLog("❌ Please enter a license key");
            return;
        }
        
        appendLog("\n=== Starting Authentication Test ===");
        statusText.setText("Status: Testing...");
        testButton.setEnabled(false);
        
        // Run comprehensive test
        AuthenticationTester.runComprehensiveTest(this, licenseKey, 
            new AuthenticationTester.TestCallback() {
                @Override
                public void onTestComplete(AuthenticationTester.TestResult result) {
                    runOnUiThread(() -> {
                        appendLog("\n=== Test Complete ===");
                        appendLog("Result: " + result.summary);
                        appendLog("Duration: " + result.testDuration + "ms");
                        appendLog("\nDetailed Log:");
                        appendLog(result.detailedLog);
                        
                        if (result.success) {
                            statusText.setText("Status: ✅ ALL TESTS PASSED");
                            appendLog("\n🎉 SUCCESS: Authentication is working correctly!");
                            appendLog("Your existing license keys are compatible.");
                            appendLog("BearMod-Loader compatibility is maintained.");
                        } else {
                            statusText.setText("Status: ❌ TESTS FAILED");
                            appendLog("\n⚠️ FAILED: Some tests failed. Check logs above.");
                            appendLog("Consider running recovery cleanup.");
                        }
                        
                        testButton.setEnabled(true);
                    });
                }
                
                @Override
                public void onTestProgress(String message) {
                    runOnUiThread(() -> {
                        appendLog("Progress: " + message);
                    });
                }
            });
    }
    
    private void runRecoveryCleanup() {
        appendLog("\n=== Running Recovery Cleanup ===");
        statusText.setText("Status: Cleaning up...");
        recoveryButton.setEnabled(false);
        
        new Thread(() -> {
            try {
                // Perform complete cleanup
                AuthRecoveryUtil.performCompleteAuthCleanup(this);
                
                runOnUiThread(() -> {
                    appendLog("✅ Recovery cleanup completed");
                    appendLog("All authentication data cleared");
                    appendLog("You can now test authentication again");
                    statusText.setText("Status: Cleanup Complete");
                    recoveryButton.setEnabled(true);
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    appendLog("❌ Recovery cleanup failed: " + e.getMessage());
                    statusText.setText("Status: Cleanup Failed");
                    recoveryButton.setEnabled(true);
                });
            }
        }).start();
    }
    
    private void generateDiagnostics() {
        appendLog("\n=== Generating Diagnostics ===");
        statusText.setText("Status: Generating diagnostics...");
        diagnosticsButton.setEnabled(false);
        
        new Thread(() -> {
            try {
                String diagnostics = AuthRecoveryUtil.generateDiagnosticReport(this);
                
                runOnUiThread(() -> {
                    appendLog("Diagnostic Report:");
                    appendLog(diagnostics);
                    statusText.setText("Status: Diagnostics Complete");
                    diagnosticsButton.setEnabled(true);
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    appendLog("❌ Diagnostics failed: " + e.getMessage());
                    statusText.setText("Status: Diagnostics Failed");
                    diagnosticsButton.setEnabled(true);
                });
            }
        }).start();
    }
    
    private void appendLog(String message) {
        Log.d(TAG, message);
        logBuilder.append(message).append("\n");
        
        runOnUiThread(() -> {
            logText.setText(logBuilder.toString());
            // Scroll to bottom
            logScrollView.post(() -> logScrollView.fullScroll(ScrollView.FOCUS_DOWN));
        });
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // Check current authentication status
        boolean isAuthenticated = authManager.isAuthenticated();
        appendLog("Current auth status: " + (isAuthenticated ? "AUTHENTICATED" : "NOT AUTHENTICATED"));
        
        // Check if BearToken exists
        SharedPreferences prefs = getSharedPreferences("bearmod_shared", MODE_PRIVATE);
        String token = prefs.getString("bear_token", null);
        long expiry = prefs.getLong("bear_expiry", 0);
        
        if (token != null && expiry > System.currentTimeMillis()) {
            long timeLeft = (expiry - System.currentTimeMillis()) / 1000;
            appendLog("BearToken exists, expires in " + timeLeft + " seconds");
        } else {
            appendLog("No valid BearToken found");
        }
    }
}
