package com.bearmod;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Switch;
import android.widget.SeekBar;
import android.content.res.ColorStateList;
import java.util.function.Consumer;
import android.widget.Spinner;
import android.widget.ArrayAdapter;
import android.widget.AdapterView;

public class MenuUIManager {
    private final Context context;
    private final LinearLayout mainLayout;
    private final LinearLayout contentLayout;
    private final ConfigurationManager configManager;

    // Color constants
    private static final String COLOR_BACKGROUND = "#23272A";
    private static final String COLOR_ACCENT = "#7289DA";
    private static final String COLOR_SUCCESS = "#43B581";
    private static final String COLOR_WARNING = "#FAA61A";
    private static final String COLOR_ERROR = "#F04747";
    private static final String COLOR_MUTED = "#99AAB5";
    private static final String COLOR_CONTAINER = "#2C2F33";

    public MenuUIManager(Context context, LinearLayout mainLayout, ConfigurationManager configManager) {
        this.context = context;
        this.mainLayout = mainLayout;
        this.contentLayout = new LinearLayout(context);
        this.configManager = configManager;
        setupMainLayout();
    }

    private void setupMainLayout() {
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackground(createBackgroundDrawable(COLOR_BACKGROUND, COLOR_ACCENT, 28));
        contentLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.addView(contentLayout);
    }

    private GradientDrawable createBackgroundDrawable(String color, String strokeColor, float cornerRadius) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setColor(Color.parseColor(color));
        if (strokeColor != null) {
            drawable.setStroke(3, Color.parseColor(strokeColor));
        }
        drawable.setCornerRadius(dpToPx(cornerRadius));
        return drawable;
    }

    public void addModernSwitch(String label, boolean initialState, Consumer<Boolean> onToggle) {
        LinearLayout container = createContainer();

        TextView labelView = createLabel(label, 16);
        labelView.setLayoutParams(new LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1.0f));

        Switch switchView = new Switch(context);
        switchView.setChecked(initialState);
        switchView.setThumbTintList(ColorStateList.valueOf(Color.WHITE));
        updateSwitchTrackColor(switchView, initialState);
        switchView.setOnCheckedChangeListener((buttonView, isChecked) -> {
            updateSwitchTrackColor(switchView, isChecked);
            onToggle.accept(isChecked);
        });

        container.addView(labelView);
        container.addView(switchView);
        addViewWithDivider(container);
    }

    private void updateSwitchTrackColor(Switch switchView, boolean isChecked) {
        switchView.setTrackTintList(ColorStateList.valueOf(
            Color.parseColor(isChecked ? COLOR_SUCCESS : COLOR_MUTED)));
    }

    public void addSeekBar(String label, String configKey, int min, int max, int defaultValue, Consumer<Integer> onStopTracking) {
        LinearLayout container = createVerticalContainer();
        TextView labelView = createLabel(label, 15);
        labelView.setPadding(0, 0, 0, dpToPx(6));

        SeekBar seekBar = new SeekBar(context);
        int currentValue = configManager.getInteger(configKey, defaultValue);
        seekBar.setMax(max - min);
        seekBar.setProgress(currentValue - min);
        seekBar.setThumbTintList(ColorStateList.valueOf(Color.parseColor(COLOR_ACCENT)));
        seekBar.setProgressTintList(ColorStateList.valueOf(Color.parseColor(COLOR_ACCENT)));

        TextView valueView = createLabel(String.valueOf(currentValue), 15);
        valueView.setTextColor(Color.parseColor(COLOR_SUCCESS));
        valueView.setPadding(0, dpToPx(4), 0, 0);

        setupSeekBarListener(seekBar, valueView, min, configKey, onStopTracking);

        container.addView(labelView);
        container.addView(seekBar);
        container.addView(valueView);
        addViewWithDivider(container);
    }

    private void setupSeekBarListener(SeekBar seekBar, TextView valueView, int min, String configKey, Consumer<Integer> onStopTracking) {
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int value = min + progress;
                valueView.setText(String.valueOf(value));
                if (fromUser) {
                    configManager.updateConfig(configKey, value);
                }
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                configManager.saveConfiguration();
                if (onStopTracking != null) {
                    onStopTracking.accept(configManager.getInteger(configKey, min));
                }
            }
        });
    }

    public void addCombo(String label, String[] options, String configKey, Consumer<Integer> onSelection) {
        LinearLayout container = createVerticalContainer();
        TextView labelView = createLabel(label, 15);
        labelView.setPadding(0, 0, 0, dpToPx(6));

        Spinner spinner = new Spinner(context);
        ArrayAdapter<String> adapter = new ArrayAdapter<>(context,
                android.R.layout.simple_spinner_dropdown_item, options);
        spinner.setAdapter(adapter);

        int initialValue = configManager.getInteger(configKey, 0);
        if (initialValue >= 0 && initialValue < options.length) {
            spinner.setSelection(initialValue);
        }

        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                configManager.updateConfig(configKey, position);
                onSelection.accept(position);
                configManager.saveConfiguration();
            }
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });

        container.addView(labelView);
        container.addView(spinner);
        addViewWithDivider(container);
    }

    private LinearLayout createContainer() {
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.HORIZONTAL);
        container.setGravity(Gravity.CENTER_VERTICAL);
        container.setPadding(dpToPx(18), dpToPx(8), dpToPx(18), dpToPx(8));
        container.setBackground(createBackgroundDrawable(COLOR_CONTAINER, null, 18));
        return container;
    }

    private LinearLayout createVerticalContainer() {
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);
        container.setPadding(dpToPx(18), dpToPx(8), dpToPx(18), dpToPx(8));
        container.setBackground(createBackgroundDrawable(COLOR_CONTAINER, null, 18));
        return container;
    }

    private TextView createLabel(String text, float textSize) {
        TextView label = new TextView(context);
        label.setText(text);
        label.setTextColor(Color.WHITE);
        label.setTextSize(textSize);
        return label;
    }

    private void addViewWithDivider(View view) {
        contentLayout.addView(view);
        addDivider();
    }

    private void addDivider() {
        View divider = new View(context);
        divider.setBackgroundColor(Color.parseColor(COLOR_BACKGROUND));
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, dpToPx(2));
        params.setMargins(dpToPx(10), dpToPx(8), dpToPx(10), dpToPx(8));
        divider.setLayoutParams(params);
        contentLayout.addView(divider);
    }

    private int dpToPx(int dp) {
        return Math.round(dp * context.getResources().getDisplayMetrics().density);
    }

    public void clearContent() {
        contentLayout.removeAllViews();
    }

    public void setVisibility(int visibility) {
        mainLayout.setVisibility(visibility);
    }

    public void addTitleBar(String title) {
        LinearLayout titleContainer = createContainer();
        titleContainer.setBackgroundColor(Color.parseColor("#1E2124"));
        titleContainer.setPadding(dpToPx(18), dpToPx(12), dpToPx(18), dpToPx(12));

        TextView titleView = createLabel(title.toUpperCase(), 18);
        titleView.setTextColor(Color.parseColor(COLOR_ACCENT));
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        titleView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        titleView.setGravity(Gravity.CENTER);

        titleContainer.addView(titleView);
        addViewWithDivider(titleContainer);
    }

    public void addMenuSection(String title, View content) {
        LinearLayout sectionContainer = createVerticalContainer();
        TextView sectionTitle = createLabel(title, 14);
        sectionTitle.setTextColor(Color.parseColor(COLOR_MUTED));
        sectionTitle.setPadding(dpToPx(6), 0, 0, dpToPx(8));

        sectionContainer.addView(sectionTitle);
        sectionContainer.addView(content);
        addViewWithDivider(sectionContainer);
    }

    public void addButton(String text, View.OnClickListener onClickListener) {
        TextView button = createLabel(text, 15);
        button.setGravity(Gravity.CENTER);
        button.setPadding(dpToPx(16), dpToPx(10), dpToPx(16), dpToPx(10));
        
        GradientDrawable buttonBackground = createBackgroundDrawable(COLOR_ACCENT, null, 8);
        android.content.res.ColorStateList rippleColor = 
            android.content.res.ColorStateList.valueOf(Color.parseColor("#99FFFFFF"));
        android.graphics.drawable.RippleDrawable ripple = 
            new android.graphics.drawable.RippleDrawable(rippleColor, buttonBackground, null);
        button.setBackground(ripple);
        button.setOnClickListener(onClickListener);

        LinearLayout buttonContainer = new LinearLayout(context);
        buttonContainer.setGravity(Gravity.CENTER);
        buttonContainer.setPadding(0, dpToPx(8), 0, dpToPx(8));
        buttonContainer.addView(button);

        addViewWithDivider(buttonContainer);
    }

    public void addInfoCard(String message, String type) {
        LinearLayout cardContainer = new LinearLayout(context);
        cardContainer.setOrientation(LinearLayout.HORIZONTAL);
        cardContainer.setGravity(Gravity.CENTER_VERTICAL);
        cardContainer.setPadding(dpToPx(16), dpToPx(12), dpToPx(16), dpToPx(12));

        String backgroundColor;
        String iconColor;
        String icon;
        switch (type.toLowerCase()) {
            case "success":
                backgroundColor = COLOR_SUCCESS;
                iconColor = "#A8FFC2";
                icon = "✓";
                break;
            case "warning":
                backgroundColor = COLOR_WARNING;
                iconColor = "#FFE1A8";
                icon = "!";
                break;
            case "error":
                backgroundColor = COLOR_ERROR;
                iconColor = "#FFA8A8";
                icon = "×";
                break;
            default: // info
                backgroundColor = COLOR_ACCENT;
                iconColor = "#A8C4FF";
                icon = "i";
                break;
        }

        cardContainer.setBackground(createBackgroundDrawable(backgroundColor + "33", null, 8));

        TextView iconView = createLabel(icon, 18);
        iconView.setTextColor(Color.parseColor(iconColor));
        iconView.setTypeface(null, android.graphics.Typeface.BOLD);
        iconView.setPadding(0, 0, dpToPx(12), 0);

        TextView messageView = createLabel(message, 14);
        messageView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ));

        cardContainer.addView(iconView);
        cardContainer.addView(messageView);

        LinearLayout wrapper = new LinearLayout(context);
        wrapper.setPadding(dpToPx(12), dpToPx(4), dpToPx(12), dpToPx(4));
        wrapper.addView(cardContainer);

        contentLayout.addView(wrapper);
    }
}