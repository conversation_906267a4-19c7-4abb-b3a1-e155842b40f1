package com.bearmod;

import android.annotation.SuppressLint;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.WindowManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.InvocationTargetException;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.lang.ref.WeakReference;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;

import android.view.View;
import android.content.Context;
import android.util.Log;
import android.database.ContentObserver;
import android.provider.MediaStore;
import android.net.Uri;
import android.content.ContentResolver;
import android.os.FileObserver;
import android.os.Environment;

/**
 * Optimized RecorderFakeUtils for BearMod
 * - Enhanced performance with caching and background processing
 * - Improved memory management and thread safety
 * - Gaming-optimized frame rate preservation
 * - Comprehensive error handling and crash prevention
 */
public class RecorderFakeUtils {
    private static final String TAG = "BearMod.RecorderFakeUtils";

    // ROM Constants
    private static final String ROM_FLYME = "Flyme";
    private static final String ROM_MIUI = "MIUI";
    private static final String ROM_BLACKSHARK = "Blackshark";
    private static final String ROM_ONEPLUS = "OnePlus";
    private static final String ROM_ROG = "ROG";
    private static final String ROM_EMUI = "EMUI";
    private static final String ROM_OPPO = "OPPO";
    private static final String ROM_SMARTISAN = "SMARTISAN";
    private static final String ROM_VIVO = "VIVO";
    private static final String ROM_NUBIAUI = "NUBIAUI";
    private static final String ROM_SAMSUNG = "ONEUI";

    // Property Keys
    private static final String KEY_VERSION_MIUI = "ro.miui.ui.version.name";
    private static final String KEY_VERSION_EMUI = "ro.build.version.emui";
    private static final String KEY_VERSION_OPPO = "ro.build.version.opporom";
    private static final String KEY_VERSION_SMARTISAN = "ro.smartisan.version";
    private static final String KEY_VERSION_VIVO = "ro.vivo.os.version";
    private static final String KEY_VERSION_NUBIA = "ro.build.nubia.rom.name";
    private static final String KEY_VERSION_ONEPLIS = "ro.build.ota.versionname";
    private static final String KEY_VERSION_SAMSUNG = "ro.channel.officehubrow";
    private static final String KEY_VERSION_BLACKSHARK = "ro.blackshark.rom";
    private static final String KEY_VERSION_ROG = "ro.build.fota.version";

    // Thread-safe state management
    private static final AtomicReference<String> sDetectedROM = new AtomicReference<>();
    private static final AtomicBoolean sInitialized = new AtomicBoolean(false);
    private static final AtomicBoolean sIsProcessing = new AtomicBoolean(false);

    // Performance optimization - cached properties and reflection objects
    private static final ConcurrentHashMap<String, String> sPropCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Field> sFieldCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Method> sMethodCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Constructor<?>> sConstructorCache = new ConcurrentHashMap<>();

    // Background processing
    private static final ExecutorService sBackgroundExecutor = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "RecorderFakeUtils-Background");
        t.setDaemon(true);
        t.setPriority(Thread.NORM_PRIORITY - 1); // Lower priority to not interfere with gaming
        return t;
    });

    // UI thread handler for safe view updates
    private static final Handler sMainHandler = new Handler(Looper.getMainLooper());

    // Gaming performance optimization - frame rate preservation
    private static final long MIN_UPDATE_INTERVAL_MS = 16; // ~60 FPS
    private static volatile long sLastUpdateTime = 0;

    // ========== Screenshot Detection and Hiding System ==========

    // Screenshot detection constants
    private static final String SCREENSHOT_PATH = Environment.getExternalStorageDirectory() + "/Pictures/Screenshots/";
    private static final String DCIM_SCREENSHOTS_PATH = Environment.getExternalStorageDirectory() + "/DCIM/Screenshots/";
    private static final long SCREENSHOT_DETECTION_TIMEOUT_MS = 3000; // 3 seconds
    private static final long SCREENSHOT_HIDE_DURATION_MS = 1000; // 1 second
    private static final long SCREENSHOT_RESTORE_DELAY_MS = 500; // 0.5 seconds

    // Screenshot detection state management
    private static final AtomicBoolean sIsScreenshotDetectionActive = new AtomicBoolean(false);
    private static final AtomicBoolean sIsScreenshotInProgress = new AtomicBoolean(false);
    private static final AtomicBoolean sIsUIHiddenForScreenshot = new AtomicBoolean(false);
    private static final AtomicLong sLastScreenshotTime = new AtomicLong(0);
    private static final AtomicLong sScreenshotDetectionStartTime = new AtomicLong(0);

    // Screenshot detection components
    private static ScheduledExecutorService sScreenshotDetectionExecutor;
    private static ContentObserver sMediaStoreObserver;
    private static FileObserver sScreenshotFileObserver;
    private static final Set<String> sHiddenViewIds = new HashSet<>();
    private static final List<WeakReference<View>> sHiddenViews = new ArrayList<>();

    // State tracking for screenshot detection (replacing setTag/getTag)
    private static final ConcurrentHashMap<View, Integer> sOriginalVisibilityMap = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<WindowManager.LayoutParams, Float> sOriginalAlphaMap = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<WindowManager.LayoutParams, Integer> sOriginalFlagsMap = new ConcurrentHashMap<>();

    // Screenshot detection callbacks
    public interface ScreenshotDetectionCallback {
        void onScreenshotDetected();
        void onScreenshotCompleted();
        void onUIHidden();
        void onUIRestored();
    }

    private static final Set<ScreenshotDetectionCallback> sScreenshotCallbacks = new HashSet<>();

    //华为
    public static boolean isEmui() {
        return check(ROM_EMUI);
    }

    //小米
    public static boolean isMiui() {
        return check(ROM_MIUI);
    }

    //vivo
    public static boolean isVivo() {
        return check(ROM_VIVO);
    }

    //oppo
    public static boolean isOppo() {
        return check(ROM_OPPO);
    }

    //魅族
    public static boolean isFlyme() {
        return check(ROM_FLYME);
    }

    //红魔
    public static boolean isNubia() {
        return check(ROM_NUBIAUI);
    }

    //一加
    public static boolean isOnePlus() {
        return check(ROM_ONEPLUS);
    }

    //三星
    public static boolean isSanSung() {
        return check(ROM_SAMSUNG) || Build.MANUFACTURER.equalsIgnoreCase("samsung");
    }

    //黑鲨
    public static boolean isBLACKSHARK() {
        return check(ROM_BLACKSHARK);
    }

    //ROG
    // public static boolean isRog() {
    //   return check(ROM_ROG);
    // }

    /**
     * Check if advanced features are active (placeholder for future implementation)
     */
    public static boolean isActivice() {
        return false;
    }

    // ========== Memory-Safe View Management ==========

    /**
     * Thread-safe view reference management using WeakReferences to prevent memory leaks
     */
    private static final class ViewReferences {
        private volatile WeakReference<WindowManager.LayoutParams> mainLayoutParams;
        private volatile WeakReference<WindowManager.LayoutParams> iconLayoutParams;
        private volatile WeakReference<WindowManager.LayoutParams> canvasLayoutParams;
        private volatile WeakReference<WindowManager> windowManager;
        private volatile WeakReference<View> mainView;
        private volatile WeakReference<View> iconView;
        private volatile WeakReference<View> canvasView;
        private volatile WeakReference<Context> context;

        // Thread-safe getters
        WindowManager.LayoutParams getMainLayoutParams() {
            return mainLayoutParams != null ? mainLayoutParams.get() : null;
        }

        WindowManager.LayoutParams getIconLayoutParams() {
            return iconLayoutParams != null ? iconLayoutParams.get() : null;
        }

        WindowManager.LayoutParams getCanvasLayoutParams() {
            return canvasLayoutParams != null ? canvasLayoutParams.get() : null;
        }

        WindowManager getWindowManager() {
            return windowManager != null ? windowManager.get() : null;
        }

        View getMainView() {
            return mainView != null ? mainView.get() : null;
        }

        View getIconView() {
            return iconView != null ? iconView.get() : null;
        }

        View getCanvasView() {
            return canvasView != null ? canvasView.get() : null;
        }

        Context getContext() {
            return context != null ? context.get() : null;
        }

        // Thread-safe setters
        void setMainLayoutParams(WindowManager.LayoutParams params) {
            this.mainLayoutParams = params != null ? new WeakReference<>(params) : null;
        }

        void setIconLayoutParams(WindowManager.LayoutParams params) {
            this.iconLayoutParams = params != null ? new WeakReference<>(params) : null;
        }

        void setCanvasLayoutParams(WindowManager.LayoutParams params) {
            this.canvasLayoutParams = params != null ? new WeakReference<>(params) : null;
        }

        void setWindowManager(WindowManager manager) {
            this.windowManager = manager != null ? new WeakReference<>(manager) : null;
        }

        void setMainView(View view) {
            this.mainView = view != null ? new WeakReference<>(view) : null;
        }

        void setIconView(View view) {
            this.iconView = view != null ? new WeakReference<>(view) : null;
        }

        void setCanvasView(View view) {
            this.canvasView = view != null ? new WeakReference<>(view) : null;
        }

        void setContext(Context ctx) {
            this.context = ctx != null ? new WeakReference<>(ctx) : null;
        }

        // Clear all references
        void clear() {
            mainLayoutParams = null;
            iconLayoutParams = null;
            canvasLayoutParams = null;
            windowManager = null;
            mainView = null;
            iconView = null;
            canvasView = null;
            context = null;
        }

        // Check if all required references are available
        boolean isValid() {
            return getWindowManager() != null &&
                   getMainLayoutParams() != null &&
                   getIconLayoutParams() != null &&
                   getCanvasLayoutParams() != null;
        }
    }

    // Single instance of view references
    private static final ViewReferences sViewRefs = new ViewReferences();

    // ========== Screenshot Detection System ==========

    /**
     * Initialize screenshot detection system
     * This should be called when the service starts
     */
    public static void initializeScreenshotDetection(Context context) {
        if (sIsScreenshotDetectionActive.get()) {
            Log.d(TAG, "Screenshot detection already active");
            return;
        }

        try {
            Log.d(TAG, "Initializing screenshot detection system");

            // Initialize screenshot detection executor
            if (sScreenshotDetectionExecutor == null || sScreenshotDetectionExecutor.isShutdown()) {
                sScreenshotDetectionExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                    Thread t = new Thread(r, "ScreenshotDetection");
                    t.setDaemon(true);
                    t.setPriority(Thread.NORM_PRIORITY);
                    return t;
                });
            }

            // Initialize MediaStore observer for screenshot detection
            initializeMediaStoreObserver(context);

            // Initialize file system observer for screenshot files
            initializeFileSystemObserver();

            // Start periodic screenshot detection
            startPeriodicScreenshotDetection();

            sIsScreenshotDetectionActive.set(true);
            Log.d(TAG, "Screenshot detection system initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing screenshot detection", e);
        }
    }

    /**
     * Initialize MediaStore observer to detect new screenshots
     */
    private static void initializeMediaStoreObserver(Context context) {
        try {
            if (sMediaStoreObserver != null) {
                return; // Already initialized
            }

            sMediaStoreObserver = new ContentObserver(sMainHandler) {
                @Override
                public void onChange(boolean selfChange, Uri uri) {
                    super.onChange(selfChange, uri);

                    if (uri != null && uri.toString().contains("external")) {
                        Log.d(TAG, "MediaStore change detected: " + uri);
                        handlePotentialScreenshot("MediaStore", uri.toString());
                    }
                }
            };

            // Register observer for external storage changes
            ContentResolver resolver = context.getContentResolver();
            resolver.registerContentObserver(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                true,
                sMediaStoreObserver
            );

            Log.d(TAG, "MediaStore observer registered");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing MediaStore observer", e);
        }
    }

    /**
     * Initialize file system observer for screenshot directories
     */
    private static void initializeFileSystemObserver() {
        try {
            if (sScreenshotFileObserver != null) {
                return; // Already initialized
            }

            // Monitor common screenshot directories
            String[] screenshotPaths = {
                SCREENSHOT_PATH,
                DCIM_SCREENSHOTS_PATH,
                Environment.getExternalStorageDirectory() + "/Pictures/",
                Environment.getExternalStorageDirectory() + "/DCIM/"
            };

            for (String path : screenshotPaths) {
                try {
                    FileObserver observer = new FileObserver(path, FileObserver.CREATE | FileObserver.CLOSE_WRITE) {
                        @Override
                        public void onEvent(int event, String fileName) {
                            if (fileName != null && isScreenshotFile(fileName)) {
                                Log.d(TAG, "Screenshot file detected: " + fileName);
                                handlePotentialScreenshot("FileSystem", path + fileName);
                            }
                        }
                    };
                    observer.startWatching();
                    Log.d(TAG, "File observer started for: " + path);
                } catch (Exception e) {
                    Log.w(TAG, "Could not monitor path: " + path, e);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error initializing file system observer", e);
        }
    }

    /**
     * Start periodic screenshot detection as fallback
     */
    private static void startPeriodicScreenshotDetection() {
        if (sScreenshotDetectionExecutor == null) {
            return;
        }

        sScreenshotDetectionExecutor.scheduleWithFixedDelay(() -> {
            try {
                checkForScreenshotActivity();
            } catch (Exception e) {
                Log.e(TAG, "Error in periodic screenshot detection", e);
            }
        }, 100, 100, TimeUnit.MILLISECONDS); // Check every 100ms
    }

    /**
     * Check if a filename indicates a screenshot
     */
    private static boolean isScreenshotFile(String fileName) {
        if (fileName == null) return false;

        String lowerName = fileName.toLowerCase();
        return lowerName.contains("screenshot") ||
               lowerName.contains("screen_") ||
               lowerName.contains("capture") ||
               (lowerName.startsWith("img_") && lowerName.contains(".jpg")) ||
               (lowerName.startsWith("screenshot_") && (lowerName.contains(".png") || lowerName.contains(".jpg")));
    }

    /**
     * Handle potential screenshot detection
     */
    private static void handlePotentialScreenshot(String source, String details) {
        if (!sIsScreenshotDetectionActive.get()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        long lastScreenshotTime = sLastScreenshotTime.get();

        // Prevent duplicate detections within short time window
        if (currentTime - lastScreenshotTime < 500) {
            return;
        }

        Log.d(TAG, "Potential screenshot detected from " + source + ": " + details);

        // Update screenshot detection time
        sLastScreenshotTime.set(currentTime);
        sScreenshotDetectionStartTime.set(currentTime);

        // Start screenshot hiding process
        initiateScreenshotHiding();
    }

    /**
     * Check for screenshot activity (fallback detection)
     */
    private static void checkForScreenshotActivity() {
        if (!sIsScreenshotDetectionActive.get()) {
            return;
        }

        // Check if we're in a screenshot hiding state that needs to be restored
        if (sIsUIHiddenForScreenshot.get()) {
            long currentTime = System.currentTimeMillis();
            long hideStartTime = sScreenshotDetectionStartTime.get();

            // Restore UI after timeout
            if (currentTime - hideStartTime > SCREENSHOT_HIDE_DURATION_MS) {
                restoreUIAfterScreenshot();
            }
        }
    }

    /**
     * Initiate the screenshot hiding process
     */
    private static void initiateScreenshotHiding() {
        if (sIsScreenshotInProgress.compareAndSet(false, true)) {
            Log.d(TAG, "Initiating screenshot hiding process");

            // Notify callbacks
            notifyScreenshotDetected();

            // Hide UI elements immediately
            hideUIForScreenshot();

            // Schedule UI restoration
            scheduleUIRestoration();
        }
    }

    /**
     * Hide UI elements for screenshot
     */
    private static void hideUIForScreenshot() {
        if (sIsUIHiddenForScreenshot.get()) {
            return; // Already hidden
        }

        try {
            Log.d(TAG, "Hiding UI elements for screenshot");

            // Hide main UI components
            hideViewSafely(sViewRefs.getMainView(), "mainView");
            hideViewSafely(sViewRefs.getIconView(), "iconView");
            hideViewSafely(sViewRefs.getCanvasView(), "canvasView");

            // Apply screenshot-specific layout parameters
            applyScreenshotLayoutParameters();

            sIsUIHiddenForScreenshot.set(true);

            // Notify callbacks
            notifyUIHidden();

            Log.d(TAG, "UI elements hidden successfully for screenshot");

        } catch (Exception e) {
            Log.e(TAG, "Error hiding UI for screenshot", e);
        }
    }

    /**
     * Safely hide a view for screenshot
     */
    private static void hideViewSafely(View view, String viewName) {
        if (view == null) {
            return;
        }

        try {
            // Store original visibility in our state map
            int originalVisibility = view.getVisibility();
            sOriginalVisibilityMap.put(view, originalVisibility);

            // Hide the view
            view.setVisibility(View.INVISIBLE);

            // Add to hidden views list for restoration
            sHiddenViews.add(new WeakReference<>(view));
            sHiddenViewIds.add(viewName);

            Log.d(TAG, "Hidden view: " + viewName);

        } catch (Exception e) {
            Log.e(TAG, "Error hiding view: " + viewName, e);
        }
    }

    /**
     * Apply screenshot-specific layout parameters
     */
    private static void applyScreenshotLayoutParameters() {
        try {
            WindowManager.LayoutParams mainParams = sViewRefs.getMainLayoutParams();
            WindowManager.LayoutParams iconParams = sViewRefs.getIconLayoutParams();
            WindowManager.LayoutParams canvasParams = sViewRefs.getCanvasLayoutParams();

            if (mainParams != null) {
                applyScreenshotParametersToLayout(mainParams, "main");
            }
            if (iconParams != null) {
                applyScreenshotParametersToLayout(iconParams, "icon");
            }
            if (canvasParams != null) {
                applyScreenshotParametersToLayout(canvasParams, "canvas");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error applying screenshot layout parameters", e);
        }
    }

    /**
     * Apply screenshot-specific parameters to a layout
     */
    private static void applyScreenshotParametersToLayout(WindowManager.LayoutParams params, String layoutName) {
        try {
            // Store original parameters in our state maps
            sOriginalAlphaMap.put(params, params.alpha);
            sOriginalFlagsMap.put(params, params.flags);

            // Make layout invisible for screenshot
            params.alpha = 0.0f;
            params.flags |= WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE;
            params.flags |= WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;

            Log.d(TAG, "Applied screenshot parameters to " + layoutName + " layout");

        } catch (Exception e) {
            Log.e(TAG, "Error applying screenshot parameters to " + layoutName, e);
        }
    }

    /**
     * Schedule UI restoration after screenshot
     */
    private static void scheduleUIRestoration() {
        if (sScreenshotDetectionExecutor == null) {
            return;
        }

        sScreenshotDetectionExecutor.schedule(() -> {
            try {
                restoreUIAfterScreenshot();
            } catch (Exception e) {
                Log.e(TAG, "Error in scheduled UI restoration", e);
            }
        }, SCREENSHOT_RESTORE_DELAY_MS, TimeUnit.MILLISECONDS);
    }

    /**
     * Restore UI after screenshot is complete
     */
    private static void restoreUIAfterScreenshot() {
        if (!sIsUIHiddenForScreenshot.get()) {
            return; // Not hidden
        }

        try {
            Log.d(TAG, "Restoring UI after screenshot");

            // Restore view visibility
            restoreHiddenViews();

            // Restore layout parameters
            restoreLayoutParameters();

            // Clear hidden views tracking
            clearHiddenViewsTracking();

            // Update state
            sIsUIHiddenForScreenshot.set(false);
            sIsScreenshotInProgress.set(false);

            // Notify callbacks
            notifyUIRestored();
            notifyScreenshotCompleted();

            Log.d(TAG, "UI restored successfully after screenshot");

        } catch (Exception e) {
            Log.e(TAG, "Error restoring UI after screenshot", e);
        }
    }

    /**
     * Restore hidden views
     */
    private static void restoreHiddenViews() {
        try {
            for (WeakReference<View> viewRef : sHiddenViews) {
                View view = viewRef.get();
                if (view != null) {
                    Integer originalVisibility = sOriginalVisibilityMap.get(view);
                    if (originalVisibility != null) {
                        view.setVisibility(originalVisibility);
                        sOriginalVisibilityMap.remove(view);
                        Log.d(TAG, "Restored view visibility");
                    } else {
                        view.setVisibility(View.VISIBLE);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error restoring hidden views", e);
        }
    }

    /**
     * Restore layout parameters
     */
    private static void restoreLayoutParameters() {
        try {
            WindowManager.LayoutParams mainParams = sViewRefs.getMainLayoutParams();
            WindowManager.LayoutParams iconParams = sViewRefs.getIconLayoutParams();
            WindowManager.LayoutParams canvasParams = sViewRefs.getCanvasLayoutParams();

            if (mainParams != null) {
                restoreLayoutParametersFromScreenshot(mainParams, "main");
            }
            if (iconParams != null) {
                restoreLayoutParametersFromScreenshot(iconParams, "icon");
            }
            if (canvasParams != null) {
                restoreLayoutParametersFromScreenshot(canvasParams, "canvas");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error restoring layout parameters", e);
        }
    }

    /**
     * Restore layout parameters from screenshot state
     */
    private static void restoreLayoutParametersFromScreenshot(WindowManager.LayoutParams params, String layoutName) {
        try {
            // Restore original alpha
            Float originalAlpha = sOriginalAlphaMap.get(params);
            if (originalAlpha != null) {
                params.alpha = originalAlpha;
                sOriginalAlphaMap.remove(params);
            } else {
                params.alpha = 1.0f;
            }

            // Restore original flags
            Integer originalFlags = sOriginalFlagsMap.get(params);
            if (originalFlags != null) {
                params.flags = originalFlags;
                sOriginalFlagsMap.remove(params);
            } else {
                // Remove screenshot-specific flags
                params.flags &= ~WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE;
                params.flags &= ~WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            }

            Log.d(TAG, "Restored parameters for " + layoutName + " layout");

        } catch (Exception e) {
            Log.e(TAG, "Error restoring parameters for " + layoutName, e);
        }
    }

    /**
     * Clear hidden views tracking
     */
    private static void clearHiddenViewsTracking() {
        try {
            sHiddenViews.clear();
            sHiddenViewIds.clear();
            Log.d(TAG, "Cleared hidden views tracking");
        } catch (Exception e) {
            Log.e(TAG, "Error clearing hidden views tracking", e);
        }
    }

    // ========== Screenshot Detection Callback Management ==========

    /**
     * Add screenshot detection callback
     */
    public static void addScreenshotDetectionCallback(ScreenshotDetectionCallback callback) {
        if (callback != null) {
            sScreenshotCallbacks.add(callback);
            Log.d(TAG, "Added screenshot detection callback");
        }
    }

    /**
     * Remove screenshot detection callback
     */
    public static void removeScreenshotDetectionCallback(ScreenshotDetectionCallback callback) {
        if (callback != null) {
            sScreenshotCallbacks.remove(callback);
            Log.d(TAG, "Removed screenshot detection callback");
        }
    }

    /**
     * Notify callbacks of screenshot detection
     */
    private static void notifyScreenshotDetected() {
        try {
            for (ScreenshotDetectionCallback callback : sScreenshotCallbacks) {
                try {
                    callback.onScreenshotDetected();
                } catch (Exception e) {
                    Log.e(TAG, "Error in screenshot detected callback", e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error notifying screenshot detected", e);
        }
    }

    /**
     * Notify callbacks of screenshot completion
     */
    private static void notifyScreenshotCompleted() {
        try {
            for (ScreenshotDetectionCallback callback : sScreenshotCallbacks) {
                try {
                    callback.onScreenshotCompleted();
                } catch (Exception e) {
                    Log.e(TAG, "Error in screenshot completed callback", e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error notifying screenshot completed", e);
        }
    }

    /**
     * Notify callbacks of UI hidden
     */
    private static void notifyUIHidden() {
        try {
            for (ScreenshotDetectionCallback callback : sScreenshotCallbacks) {
                try {
                    callback.onUIHidden();
                } catch (Exception e) {
                    Log.e(TAG, "Error in UI hidden callback", e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error notifying UI hidden", e);
        }
    }

    /**
     * Notify callbacks of UI restored
     */
    private static void notifyUIRestored() {
        try {
            for (ScreenshotDetectionCallback callback : sScreenshotCallbacks) {
                try {
                    callback.onUIRestored();
                } catch (Exception e) {
                    Log.e(TAG, "Error in UI restored callback", e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error notifying UI restored", e);
        }
    }

    // ========== Public Screenshot Detection API ==========

    /**
     * Check if screenshot detection is active
     */
    public static boolean isScreenshotDetectionActive() {
        return sIsScreenshotDetectionActive.get();
    }

    /**
     * Check if a screenshot is currently in progress
     */
    public static boolean isScreenshotInProgress() {
        return sIsScreenshotInProgress.get();
    }

    /**
     * Check if UI is currently hidden for screenshot
     */
    public static boolean isUIHiddenForScreenshot() {
        return sIsUIHiddenForScreenshot.get();
    }

    /**
     * Manually trigger screenshot hiding (for testing or manual control)
     */
    public static void manuallyTriggerScreenshotHiding() {
        Log.d(TAG, "Manually triggering screenshot hiding");
        handlePotentialScreenshot("Manual", "User triggered");
    }

    /**
     * Manually restore UI from screenshot hiding (for testing or manual control)
     */
    public static void manuallyRestoreUIFromScreenshot() {
        Log.d(TAG, "Manually restoring UI from screenshot hiding");
        restoreUIAfterScreenshot();
    }

    /**
     * Stop screenshot detection system
     */
    public static void stopScreenshotDetection(Context context) {
        if (!sIsScreenshotDetectionActive.get()) {
            return;
        }

        try {
            Log.d(TAG, "Stopping screenshot detection system");

            // Stop detection
            sIsScreenshotDetectionActive.set(false);

            // Restore UI if currently hidden
            if (sIsUIHiddenForScreenshot.get()) {
                restoreUIAfterScreenshot();
            }

            // Cleanup observers
            cleanupScreenshotDetectionObservers(context);

            // Shutdown executor
            if (sScreenshotDetectionExecutor != null && !sScreenshotDetectionExecutor.isShutdown()) {
                sScreenshotDetectionExecutor.shutdown();
                try {
                    if (!sScreenshotDetectionExecutor.awaitTermination(1, TimeUnit.SECONDS)) {
                        sScreenshotDetectionExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    sScreenshotDetectionExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            Log.d(TAG, "Screenshot detection system stopped");

        } catch (Exception e) {
            Log.e(TAG, "Error stopping screenshot detection", e);
        }
    }

    /**
     * Cleanup screenshot detection observers
     */
    private static void cleanupScreenshotDetectionObservers(Context context) {
        try {
            // Unregister MediaStore observer
            if (sMediaStoreObserver != null && context != null) {
                ContentResolver resolver = context.getContentResolver();
                resolver.unregisterContentObserver(sMediaStoreObserver);
                sMediaStoreObserver = null;
                Log.d(TAG, "MediaStore observer unregistered");
            }

            // Stop file observer
            if (sScreenshotFileObserver != null) {
                sScreenshotFileObserver.stopWatching();
                sScreenshotFileObserver = null;
                Log.d(TAG, "File observer stopped");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up screenshot detection observers", e);
        }
    }

    /**
     * Get screenshot detection statistics
     */
    public static String getScreenshotDetectionStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("Screenshot Detection Statistics:\n");
            stats.append("  Detection Active: ").append(sIsScreenshotDetectionActive.get()).append("\n");
            stats.append("  Screenshot In Progress: ").append(sIsScreenshotInProgress.get()).append("\n");
            stats.append("  UI Hidden: ").append(sIsUIHiddenForScreenshot.get()).append("\n");
            stats.append("  Last Screenshot Time: ").append(sLastScreenshotTime.get()).append("\n");
            stats.append("  Hidden Views Count: ").append(sHiddenViews.size()).append("\n");
            stats.append("  Callbacks Count: ").append(sScreenshotCallbacks.size()).append("\n");
            return stats.toString();
        } catch (Exception e) {
            Log.e(TAG, "Error getting screenshot detection stats", e);
            return "Error getting stats: " + e.getMessage();
        }
    }




    /**
     * Optimized method to set fake recorder window layout parameters
     * - Gaming performance optimized with frame rate preservation
     * - Background processing for heavy operations
     * - Comprehensive error handling and crash prevention
     * - Memory-safe view management
     */
    public static void setFakeRecorderWindowLayoutParams(
            WindowManager.LayoutParams mainViewLayoutParams,
            WindowManager.LayoutParams iconViewLayoutParams,
            WindowManager.LayoutParams canvasViewLayoutParams,
            WindowManager manager,
            View mainView,
            View iconView,
            View canvasView,
            Context context) {

        Log.d(TAG, "Setting fake recorder window layout params");

        // Frame rate preservation - throttle updates
        long currentTime = System.currentTimeMillis();
        if (currentTime - sLastUpdateTime < MIN_UPDATE_INTERVAL_MS) {
            Log.d(TAG, "Throttling update to preserve frame rate");
            return;
        }
        sLastUpdateTime = currentTime;

        // Prevent concurrent processing
        if (!sIsProcessing.compareAndSet(false, true)) {
            Log.d(TAG, "Already processing, skipping duplicate request");
            return;
        }

        try {
            // Validate input parameters
            if (!validateInputParameters(mainViewLayoutParams, iconViewLayoutParams,
                    canvasViewLayoutParams, manager, mainView, iconView, canvasView, context)) {
                Log.e(TAG, "Invalid input parameters, aborting operation");
                return;
            }

            // Store references using memory-safe approach
            storeViewReferences(mainViewLayoutParams, iconViewLayoutParams, canvasViewLayoutParams,
                    manager, mainView, iconView, canvasView, context);

            // Initialize ROM detection in background if not done
            initializeROMDetectionAsync();

            // Apply layout parameters with optimized approach
            applyLayoutParametersOptimized(true);

        } catch (Exception e) {
            Log.e(TAG, "Critical error in setFakeRecorderWindowLayoutParams", e);
            // Attempt recovery
            recoverFromError();
        } finally {
            sIsProcessing.set(false);
        }
    }

    /**
     * Validate input parameters to prevent crashes
     */
    private static boolean validateInputParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            WindowManager manager,
            View mainView,
            View iconView,
            View canvasView,
            Context context) {

        if (manager == null) {
            Log.e(TAG, "WindowManager is null");
            return false;
        }

        if (mainParams == null || iconParams == null || canvasParams == null) {
            Log.e(TAG, "One or more LayoutParams are null");
            return false;
        }

        if (mainView == null || iconView == null || canvasView == null) {
            Log.e(TAG, "One or more Views are null");
            return false;
        }

        if (context == null) {
            Log.e(TAG, "Context is null");
            return false;
        }

        return true;
    }

    /**
     * Store view references using memory-safe approach
     */
    private static void storeViewReferences(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            WindowManager manager,
            View mainView,
            View iconView,
            View canvasView,
            Context context) {

        sViewRefs.setMainLayoutParams(mainParams);
        sViewRefs.setIconLayoutParams(iconParams);
        sViewRefs.setCanvasLayoutParams(canvasParams);
        sViewRefs.setWindowManager(manager);
        sViewRefs.setMainView(mainView);
        sViewRefs.setIconView(iconView);
        sViewRefs.setCanvasView(canvasView);
        sViewRefs.setContext(context);

        Log.d(TAG, "View references stored successfully");
    }

    /**
     * Initialize ROM detection asynchronously to avoid blocking UI thread
     */
    private static void initializeROMDetectionAsync() {
        if (sInitialized.get()) {
            return; // Already initialized
        }

        sBackgroundExecutor.execute(() -> {
            try {
                Log.d(TAG, "Initializing ROM detection in background");
                String detectedROM = detectROMType();
                sDetectedROM.set(detectedROM);
                sInitialized.set(true);
                Log.d(TAG, "ROM detection completed: " + detectedROM);
            } catch (Exception e) {
                Log.e(TAG, "Error during ROM detection", e);
                sDetectedROM.set("UNKNOWN");
                sInitialized.set(true);
            }
        });
    }

    /**
     * Apply layout parameters with optimized approach
     */
    private static void applyLayoutParametersOptimized(boolean isSet) {
        if (!sViewRefs.isValid()) {
            Log.e(TAG, "View references are not valid, cannot apply layout parameters");
            return;
        }

        try {
            // Get cached window title
            String windowTitle = getCachedWindowTitle();

            // Apply parameters to all views in background, then update UI on main thread
            sBackgroundExecutor.execute(() -> {
                try {
                    // Prepare layout parameters in background
                    prepareLayoutParameters(windowTitle, isSet);

                    // Update views on main thread
                    sMainHandler.post(() -> {
                        try {
                            updateViewLayoutsSafely();
                        } catch (Exception e) {
                            Log.e(TAG, "Error updating view layouts on main thread", e);
                        }
                    });

                } catch (Exception e) {
                    Log.e(TAG, "Error preparing layout parameters in background", e);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error in applyLayoutParametersOptimized", e);
        }
    }

    /**
     * Prepare layout parameters in background thread
     */
    private static void prepareLayoutParameters(String windowTitle, boolean isSet) {
        try {
            WindowManager.LayoutParams mainParams = sViewRefs.getMainLayoutParams();
            WindowManager.LayoutParams iconParams = sViewRefs.getIconLayoutParams();
            WindowManager.LayoutParams canvasParams = sViewRefs.getCanvasLayoutParams();

            if (mainParams == null || iconParams == null || canvasParams == null) {
                Log.e(TAG, "Layout parameters are null, cannot prepare");
                return;
            }

            // Set window titles
            mainParams.setTitle(windowTitle);
            iconParams.setTitle(windowTitle);
            canvasParams.setTitle(windowTitle);

            // Apply ROM-specific parameters
            String romType = sDetectedROM.get();
            if (romType != null) {
                applyROMSpecificParameters(mainParams, iconParams, canvasParams, romType, isSet);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error preparing layout parameters", e);
        }
    }

    /**
     * Update view layouts safely on main thread
     */
    private static void updateViewLayoutsSafely() {
        try {
            View mainView = sViewRefs.getMainView();
            View iconView = sViewRefs.getIconView();
            View canvasView = sViewRefs.getCanvasView();
            WindowManager.LayoutParams mainParams = sViewRefs.getMainLayoutParams();
            WindowManager.LayoutParams iconParams = sViewRefs.getIconLayoutParams();
            WindowManager.LayoutParams canvasParams = sViewRefs.getCanvasLayoutParams();

            if (mainView != null && mainParams != null) {
                updateViewLayoutOptimized(mainView, mainParams);
            }
            if (iconView != null && iconParams != null) {
                updateViewLayoutOptimized(iconView, iconParams);
            }
            if (canvasView != null && canvasParams != null) {
                updateViewLayoutOptimized(canvasView, canvasParams);
            }

            Log.d(TAG, "View layouts updated successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error updating view layouts", e);
        }
    }

    /**
     * Optimized ROM type detection with caching
     */
    private static String detectROMType() {
        try {
            // Check cached properties first
            if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_MIUI))) {
                return ROM_MIUI;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_BLACKSHARK))) {
                return ROM_BLACKSHARK;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_EMUI))) {
                return ROM_EMUI;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_OPPO))) {
                return ROM_OPPO;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_VIVO))) {
                return ROM_VIVO;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_SMARTISAN))) {
                return ROM_SMARTISAN;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_NUBIA))) {
                return ROM_NUBIAUI;
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_ONEPLIS))) {
                String onePlusProp = getCachedProp(KEY_VERSION_ONEPLIS);
                if (onePlusProp != null && onePlusProp.toLowerCase().contains("hydrogen")) {
                    return ROM_ONEPLUS;
                }
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_ROG))) {
                String rogProp = getCachedProp(KEY_VERSION_ROG);
                if (rogProp != null && rogProp.toLowerCase().contains("CN_Phone")) {
                    return ROM_ROG;
                }
            } else if (!TextUtils.isEmpty(getCachedProp(KEY_VERSION_SAMSUNG))) {
                return ROM_SAMSUNG;
            }

            // Fallback to Build.DISPLAY check
            String sVersion = Build.DISPLAY;
            if (sVersion.toUpperCase().contains(ROM_FLYME)) {
                return ROM_FLYME;
            } else {
                return Build.MANUFACTURER.toUpperCase();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error detecting ROM type", e);
            return "UNKNOWN";
        }
    }

    /**
     * Get cached window title for performance
     */
    private static String getCachedWindowTitle() {
        String romType = sDetectedROM.get();
        if (romType == null) {
            return "";
        }

        return switch (romType) {
            case ROM_MIUI -> "com.miui.screenrecorder";
            case ROM_EMUI -> "ScreenRecoderTimer";
            case ROM_OPPO -> "com.coloros.screenrecorder.FloatView";
            case ROM_VIVO -> "screen_record_menu";
            case ROM_ONEPLUS -> "op_screenrecord";
            case ROM_FLYME -> "SysScreenRecorder";
            case ROM_NUBIAUI -> "NubiaScreenDecorOverlay";
            case ROM_BLACKSHARK -> "com.blackshark.screenrecorder";
            case ROM_ROG -> "com.asus.force.layer.transparent.SR.floatingpanel";
            default -> "";
        };
    }

    /**
     * Apply ROM-specific parameters with caching and error handling
     */
    private static void applyROMSpecificParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            String romType,
            boolean isSet) {

        try {
            switch (romType) {
                case ROM_FLYME:
                    applyMeizuParameters(mainParams, iconParams, canvasParams, isSet);
                    break;
                case ROM_MIUI:
                case ROM_BLACKSHARK:
                    applyXiaomiParameters(mainParams, iconParams, canvasParams, isSet);
                    break;
                case ROM_ONEPLUS:
                    if (isActivice() || Build.VERSION.SDK_INT == 35) {
                        applyOnePlusParameters(mainParams, iconParams, canvasParams, isSet);
                    }
                    break;
                case ROM_SAMSUNG:
                    applySamsungParameters(mainParams, iconParams, canvasParams, isSet);
                    break;
                case ROM_EMUI:
                    applyHuaweiParameters(mainParams, iconParams, canvasParams, isSet);
                    break;
                case ROM_OPPO:
                    applyOppoParameters(mainParams, iconParams, canvasParams, isSet);
                    break;
                case ROM_VIVO:
                    applyVivoParameters(mainParams, iconParams, canvasParams, isSet);
                    break;
                default:
                    Log.d(TAG, "No specific parameters for ROM: " + romType);
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying ROM-specific parameters for " + romType, e);
        }
    }

    /**
     * Optimized view layout update with error recovery
     */
    private static void updateViewLayoutOptimized(View view, WindowManager.LayoutParams params) {
        WindowManager windowManager = sViewRefs.getWindowManager();
        if (windowManager == null || view == null || params == null) {
            Log.e(TAG, "Cannot update view layout - null references");
            return;
        }

        try {
            // Set proper window type for overlay
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;

            // Try to update existing view first
            windowManager.updateViewLayout(view, params);

        } catch (IllegalArgumentException e) {
            Log.w(TAG, "View layout update failed, attempting recovery", e);

            // Recovery: remove and re-add view
            try {
                windowManager.removeView(view);
                windowManager.addView(view, params);
                Log.d(TAG, "View layout recovery successful");

            } catch (Exception recoveryException) {
                Log.e(TAG, "View layout recovery failed", recoveryException);
            }

        } catch (Exception e) {
            Log.e(TAG, "Unexpected error updating view layout", e);
        }
    }

    /**
     * Error recovery mechanism
     */
    private static void recoverFromError() {
        try {
            Log.d(TAG, "Attempting error recovery");

            // Clear cached data
            sPropCache.clear();
            sFieldCache.clear();
            sMethodCache.clear();
            sConstructorCache.clear();

            // Reset state
            sInitialized.set(false);
            sDetectedROM.set(null);

            Log.d(TAG, "Error recovery completed");

        } catch (Exception e) {
            Log.e(TAG, "Error during recovery", e);
        }
    }

    /**
     * Optimized unset method with same performance improvements
     */
    public static void unsetFakeRecorderWindowLayoutParams(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            WindowManager windowManager,
            View mainView,
            View iconView,
            View canvasView,
            Context context) {

        Log.d(TAG, "Unsetting fake recorder window layout params");

        // Frame rate preservation - throttle updates
        long currentTime = System.currentTimeMillis();
        if (currentTime - sLastUpdateTime < MIN_UPDATE_INTERVAL_MS) {
            Log.d(TAG, "Throttling unset update to preserve frame rate");
            return;
        }
        sLastUpdateTime = currentTime;

        // Prevent concurrent processing
        if (!sIsProcessing.compareAndSet(false, true)) {
            Log.d(TAG, "Already processing, skipping duplicate unset request");
            return;
        }

        try {
            // Validate input parameters
            if (!validateInputParameters(mainParams, iconParams, canvasParams,
                    windowManager, mainView, iconView, canvasView, context)) {
                Log.e(TAG, "Invalid input parameters for unset, aborting operation");
                return;
            }

            // Store references using memory-safe approach
            storeViewReferences(mainParams, iconParams, canvasParams,
                    windowManager, mainView, iconView, canvasView, context);

            // Initialize ROM detection in background if not done
            initializeROMDetectionAsync();

            // Apply layout parameters with optimized approach (unset mode)
            applyLayoutParametersOptimized(false);

        } catch (Exception e) {
            Log.e(TAG, "Critical error in unsetFakeRecorderWindowLayoutParams", e);
            // Attempt recovery
            recoverFromError();
        } finally {
            sIsProcessing.set(false);
        }
    }

    // ========== Optimized Helper Methods ==========

    /**
     * Get cached property value with background loading
     */
    private static String getCachedProp(String name) {
        String cached = sPropCache.get(name);
        if (cached != null) {
            return cached;
        }

        // Load property in background to avoid blocking
        sBackgroundExecutor.execute(() -> {
            try {
                String value = getPropSafely(name);
                if (value != null) {
                    sPropCache.put(name, value);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error caching property: " + name, e);
            }
        });

        // Return immediate result for first call
        return getPropSafely(name);
    }

    /**
     * Safely get system property without blocking UI thread
     */
    private static String getPropSafely(String name) {
        BufferedReader input = null;
        try {
            Process p = Runtime.getRuntime().exec("getprop " + name);
            input = new BufferedReader(new InputStreamReader(p.getInputStream()), 1024);
            String line = input.readLine();
            return line;
        } catch (IOException e) {
            Log.w(TAG, "Failed to get property: " + name, e);
            return null;
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    Log.w(TAG, "Error closing input stream", e);
                }
            }
        }
    }

    // ========== Optimized ROM-Specific Parameter Methods ==========

    /**
     * Apply Meizu parameters with caching and error handling
     */
    private static void applyMeizuParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            if (isSet) {
                setMeizuParamsOptimized(mainParams);
                setMeizuParamsOptimized(iconParams);
                setMeizuParamsOptimized(canvasParams);

                if (isActivice()) {
                    setMeizuParamsNewOptimized(mainParams);
                    setMeizuParamsNewOptimized(iconParams);
                    setMeizuParamsNewOptimized(canvasParams);
                }
            } else {
                unsetMeizuParamsOptimized(mainParams);
                unsetMeizuParamsOptimized(iconParams);
                unsetMeizuParamsOptimized(canvasParams);

                if (isActivice()) {
                    unsetMeizuParamsNewOptimized(mainParams);
                    unsetMeizuParamsNewOptimized(iconParams);
                    unsetMeizuParamsNewOptimized(canvasParams);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying Meizu parameters", e);
        }
    }

    /**
     * Apply Xiaomi parameters with caching and error handling
     */
    private static void applyXiaomiParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            if (isSet) {
                setXiaomiParamsOptimized(mainParams, 6666);
                setXiaomiParamsOptimized(iconParams, 6666);
                setXiaomiParamsOptimized(canvasParams, 6666);
            } else {
                unsetXiaomiParamsOptimized(mainParams, 6666);
                unsetXiaomiParamsOptimized(iconParams, 6666);
                unsetXiaomiParamsOptimized(canvasParams, 6666);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying Xiaomi parameters", e);
        }
    }

    /**
     * Apply OnePlus parameters with caching and error handling
     */
    private static void applyOnePlusParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            Field cornersOverlayFlag = getCachedField(
                mainParams.getClass(), "PRIVATE_FLAG_IS_ROUNDED_CORNERS_OVERLAY");

            if (cornersOverlayFlag != null) {
                int flagValue = (int) cornersOverlayFlag.get(mainParams.getClass());

                if (isSet) {
                    setOnePlusParamsOptimized(mainParams, flagValue);
                    setOnePlusParamsOptimized(iconParams, flagValue);
                    setOnePlusParamsOptimized(canvasParams, flagValue);
                } else {
                    unsetOnePlusParamsOptimized(mainParams, flagValue);
                    unsetOnePlusParamsOptimized(iconParams, flagValue);
                    unsetOnePlusParamsOptimized(canvasParams, flagValue);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying OnePlus parameters", e);
        }
    }

    /**
     * Apply Samsung parameters with caching and error handling
     */
    private static void applySamsungParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            if (isSet) {
                setSamsungFlagsOptimized(mainParams);
                setSamsungFlagsOptimized(iconParams);
                setSamsungFlagsOptimized(canvasParams);
            } else {
                unsetSamsungFlagsOptimized(mainParams);
                unsetSamsungFlagsOptimized(iconParams);
                unsetSamsungFlagsOptimized(canvasParams);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying Samsung parameters", e);
        }
    }

    /**
     * Apply Huawei parameters with caching and error handling
     */
    private static void applyHuaweiParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            if (isSet) {
                setHuaweiParamsOptimized(mainParams);
                setHuaweiParamsOptimized(iconParams);
                setHuaweiParamsOptimized(canvasParams);
            } else {
                unsetHuaweiParamsOptimized(mainParams);
                unsetHuaweiParamsOptimized(iconParams);
                unsetHuaweiParamsOptimized(canvasParams);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying Huawei parameters", e);
        }
    }

    /**
     * Apply Oppo parameters with caching and error handling
     */
    private static void applyOppoParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            if (isSet) {
                setOppoParamsOptimized(mainParams);
                setOppoParamsOptimized(iconParams);
                setOppoParamsOptimized(canvasParams);
            } else {
                unsetOppoParamsOptimized(mainParams);
                unsetOppoParamsOptimized(iconParams);
                unsetOppoParamsOptimized(canvasParams);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying Oppo parameters", e);
        }
    }

    /**
     * Apply Vivo parameters with caching and error handling
     */
    private static void applyVivoParameters(
            WindowManager.LayoutParams mainParams,
            WindowManager.LayoutParams iconParams,
            WindowManager.LayoutParams canvasParams,
            boolean isSet) {

        try {
            if (isSet) {
                setVivoParamsOptimized(mainParams);
                setVivoParamsOptimized(iconParams);
                setVivoParamsOptimized(canvasParams);
            } else {
                unsetVivoParamsOptimized(mainParams);
                unsetVivoParamsOptimized(iconParams);
                unsetVivoParamsOptimized(canvasParams);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error applying Vivo parameters", e);
        }
    }

    // ========== Optimized Reflection Caching Methods ==========

    /**
     * Get cached Field object to avoid repeated reflection calls
     */
    private static Field getCachedField(Class<?> clazz, String fieldName) {
        String key = clazz.getName() + "." + fieldName;
        Field cached = sFieldCache.get(key);

        if (cached != null) {
            return cached;
        }

        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            sFieldCache.put(key, field);
            return field;
        } catch (NoSuchFieldException e) {
            Log.w(TAG, "Field not found: " + key, e);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error getting field: " + key, e);
            return null;
        }
    }

    /**
     * Get cached Method object to avoid repeated reflection calls
     */
    private static Method getCachedMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        String key = clazz.getName() + "." + methodName;
        Method cached = sMethodCache.get(key);

        if (cached != null) {
            return cached;
        }

        try {
            Method method = clazz.getMethod(methodName, parameterTypes);
            method.setAccessible(true);
            sMethodCache.put(key, method);
            return method;
        } catch (NoSuchMethodException e) {
            Log.w(TAG, "Method not found: " + key, e);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error getting method: " + key, e);
            return null;
        }
    }

    /**
     * Get cached Constructor object to avoid repeated reflection calls
     */
    private static Constructor<?> getCachedConstructor(Class<?> clazz, Class<?>... parameterTypes) {
        String key = clazz.getName() + ".constructor";
        Constructor<?> cached = sConstructorCache.get(key);

        if (cached != null) {
            return cached;
        }

        try {
            Constructor<?> constructor = clazz.getConstructor(parameterTypes);
            constructor.setAccessible(true);
            sConstructorCache.put(key, constructor);
            return constructor;
        } catch (NoSuchMethodException e) {
            Log.w(TAG, "Constructor not found: " + key, e);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error getting constructor: " + key, e);
            return null;
        }
    }

    // ========== Optimized ROM-Specific Implementation Methods ==========

    /**
     * Optimized Xiaomi parameter setting with caching
     */
    private static void setXiaomiParamsOptimized(WindowManager.LayoutParams params, int flagValue) {
        try {
            // Use hardware acceleration flag as alternative
            params.flags |= WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Xiaomi parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Xiaomi parameters", e);
        }
    }

    /**
     * Optimized Xiaomi parameter unsetting with caching
     */
    private static void unsetXiaomiParamsOptimized(WindowManager.LayoutParams params, int flagValue) {
        try {
            params.flags &= ~WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Xiaomi parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Xiaomi parameters", e);
        }
    }

    /**
     * Optimized Huawei parameter setting with caching
     */
    private static void setHuaweiParamsOptimized(WindowManager.LayoutParams params) {
        try {
            params.flags |= WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Huawei parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Huawei parameters", e);
        }
    }

    /**
     * Optimized Huawei parameter unsetting with caching
     */
    private static void unsetHuaweiParamsOptimized(WindowManager.LayoutParams params) {
        try {
            params.flags &= ~WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Huawei parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Huawei parameters", e);
        }
    }

    /**
     * Optimized Oppo parameter setting with caching
     */
    private static void setOppoParamsOptimized(WindowManager.LayoutParams params) {
        try {
            params.flags |= WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Oppo parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Oppo parameters", e);
        }
    }

    /**
     * Optimized Oppo parameter unsetting with caching
     */
    private static void unsetOppoParamsOptimized(WindowManager.LayoutParams params) {
        try {
            params.flags &= ~WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Oppo parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Oppo parameters", e);
        }
    }

    /**
     * Optimized Vivo parameter setting with caching
     */
    private static void setVivoParamsOptimized(WindowManager.LayoutParams params) {
        try {
            params.flags |= WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Vivo parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Vivo parameters", e);
        }
    }

    /**
     * Optimized Vivo parameter unsetting with caching
     */
    private static void unsetVivoParamsOptimized(WindowManager.LayoutParams params) {
        try {
            params.flags &= ~WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED;
            Log.d(TAG, "Vivo parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Vivo parameters", e);
        }
    }

    /**
     * Optimized Meizu parameter setting with caching
     */
    @SuppressLint("PrivateApi")
    private static void setMeizuParamsOptimized(WindowManager.LayoutParams params) {
        try {
            Class<?> meizuParamsClass = Class.forName("android.view.MeizuLayoutParams");
            Field flagField = getCachedField(meizuParamsClass, "flags");
            Constructor<?> constructor = getCachedConstructor(meizuParamsClass);

            if (flagField != null && constructor != null) {
                Object meizuParams = constructor.newInstance();
                flagField.setInt(meizuParams, 8192);
                Field mzParamsField = getCachedField(params.getClass(), "meizuParams");
                if (mzParamsField != null) {
                    mzParamsField.set(params, meizuParams);
                }
            }
            Log.d(TAG, "Meizu parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Meizu parameters", e);
        }
    }

    /**
     * Optimized Meizu parameter unsetting with caching
     */
    @SuppressLint("PrivateApi")
    private static void unsetMeizuParamsOptimized(WindowManager.LayoutParams params) {
        try {
            Class<?> meizuParamsClass = Class.forName("android.view.MeizuLayoutParams");
            Field flagField = getCachedField(meizuParamsClass, "flags");
            Constructor<?> constructor = getCachedConstructor(meizuParamsClass);

            if (flagField != null && constructor != null) {
                Object meizuParams = constructor.newInstance();
                int currentFlags = flagField.getInt(meizuParams);
                flagField.setInt(meizuParams, currentFlags & ~8192);
                Field mzParamsField = getCachedField(params.getClass(), "meizuParams");
                if (mzParamsField != null) {
                    mzParamsField.set(params, meizuParams);
                }
            }
            Log.d(TAG, "Meizu parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Meizu parameters", e);
        }
    }

    /**
     * Optimized Meizu new parameter setting with caching
     */
    private static void setMeizuParamsNewOptimized(WindowManager.LayoutParams params) {
        try {
            Field mzParamsField = getCachedField(params.getClass(), "meizuFlags");
            if (mzParamsField != null) {
                mzParamsField.setInt(params, 1024);
            }
            Log.d(TAG, "Meizu new parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Meizu new parameters", e);
        }
    }

    /**
     * Optimized Meizu new parameter unsetting with caching
     */
    private static void unsetMeizuParamsNewOptimized(WindowManager.LayoutParams params) {
        try {
            Field mzParamsField = getCachedField(params.getClass(), "meizuFlags");
            if (mzParamsField != null) {
                int currentFlags = mzParamsField.getInt(params);
                mzParamsField.setInt(params, currentFlags & ~1024);
            }
            Log.d(TAG, "Meizu new parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Meizu new parameters", e);
        }
    }

    /**
     * Optimized OnePlus parameter setting with caching
     */
    private static void setOnePlusParamsOptimized(WindowManager.LayoutParams params, int flagValue) {
        try {
            Field flagField = getCachedField(params.getClass(), "privateFlags");
            if (flagField != null) {
                flagField.set(params, flagValue);
            }
            Log.d(TAG, "OnePlus parameters set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting OnePlus parameters", e);
        }
    }

    /**
     * Optimized OnePlus parameter unsetting with caching
     */
    private static void unsetOnePlusParamsOptimized(WindowManager.LayoutParams params, int flagValue) {
        try {
            Field flagField = getCachedField(params.getClass(), "privateFlags");
            if (flagField != null) {
                int currentFlags = flagField.getInt(params);
                flagField.set(params, currentFlags & ~flagValue);
            }
            Log.d(TAG, "OnePlus parameters unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting OnePlus parameters", e);
        }
    }

    /**
     * Optimized Samsung flags setting with caching
     */
    private static void setSamsungFlagsOptimized(WindowManager.LayoutParams params) {
        try {
            Method semAddExtensionFlags = getCachedMethod(params.getClass(), "semAddExtensionFlags", Integer.TYPE);
            Method semAddPrivateFlags = getCachedMethod(params.getClass(), "semAddPrivateFlags", Integer.TYPE);

            if (semAddExtensionFlags != null && semAddPrivateFlags != null) {
                semAddExtensionFlags.invoke(params, -2147352576);
                semAddPrivateFlags.invoke(params, params.flags);
            }
            Log.d(TAG, "Samsung flags set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting Samsung flags", e);
        }
    }

    /**
     * Optimized Samsung flags unsetting with caching
     */
    private static void unsetSamsungFlagsOptimized(WindowManager.LayoutParams params) {
        try {
            Method semRemoveExtensionFlags = getCachedMethod(params.getClass(), "semRemoveExtensionFlags", Integer.TYPE);
            Method semRemovePrivateFlags = getCachedMethod(params.getClass(), "semRemovePrivateFlags", Integer.TYPE);

            if (semRemoveExtensionFlags != null && semRemovePrivateFlags != null) {
                semRemoveExtensionFlags.invoke(params, -2147352576);
                semRemovePrivateFlags.invoke(params, params.flags);
            }
            Log.d(TAG, "Samsung flags unset successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error unsetting Samsung flags", e);
        }
    }

    // ========== Utility Methods ==========

    /**
     * Get optimized fake record window title
     */
    private static String getFakeRecordWindowTitle() {
        String romType = sDetectedROM.get();
        if (romType != null) {
            return getCachedWindowTitle();
        }

        // Fallback to legacy method if ROM not detected yet
        return getFakeRecordWindowTitleLegacy();
    }

    /**
     * Legacy method for getting window title (for backward compatibility)
     */
    private static String getFakeRecordWindowTitleLegacy() {
        if (sDetectedROM.get() == null) {
            checkROMTypeLegacy("");
        }
        String romType = sDetectedROM.get();
        if (romType == null) {
            return "";
        }
        return switch (romType) {
            case ROM_MIUI -> "com.miui.screenrecorder";
            case ROM_EMUI -> "ScreenRecoderTimer";
            case ROM_OPPO -> "com.coloros.screenrecorder.FloatView";
            case ROM_VIVO -> "screen_record_menu";
            case ROM_ONEPLUS -> "op_screenrecord";
            case ROM_FLYME -> "SysScreenRecorder";
            case ROM_NUBIAUI -> "NubiaScreenDecorOverlay";
            case ROM_BLACKSHARK -> "com.blackshark.screenrecorder";
            case ROM_ROG -> "com.asus.force.layer.transparent.SR.floatingpanel";
            default -> "";
        };
    }

    /**
     * Optimized ROM type checking with caching
     */
    private static boolean check(String rom) {
        String detectedROM = sDetectedROM.get();
        if (detectedROM != null) {
            return detectedROM.equals(rom);
        }

        // Initialize ROM detection if not done
        if (!sInitialized.get()) {
            initializeROMDetectionAsync();
        }

        // Fallback to legacy method for immediate result
        return checkROMTypeLegacy(rom);
    }

    /**
     * Legacy ROM checking method (for backward compatibility)
     */
    private static boolean checkROMTypeLegacy(String rom) {
        String currentROM = sDetectedROM.get();
        if (currentROM != null) {
            return currentROM.equals(rom);
        }

        if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_MIUI))) {
            currentROM = ROM_MIUI;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_BLACKSHARK))) {
            currentROM = ROM_BLACKSHARK;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_EMUI))) {
            currentROM = ROM_EMUI;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_OPPO))) {
            currentROM = ROM_OPPO;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_VIVO))) {
            currentROM = ROM_VIVO;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_SMARTISAN))) {
            currentROM = ROM_SMARTISAN;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_NUBIA))) {
            currentROM = ROM_NUBIAUI;
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_ONEPLIS))) {
            String onePlusProp = getPropSafely(KEY_VERSION_ONEPLIS);
            if (onePlusProp != null && onePlusProp.toLowerCase().contains("hydrogen")) {
                currentROM = ROM_ONEPLUS;
            }
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_ROG))) {
            String rogProp = getPropSafely(KEY_VERSION_ROG);
            if (rogProp != null && rogProp.toLowerCase().contains("CN_Phone")) {
                currentROM = ROM_ROG;
            }
        } else if (!TextUtils.isEmpty(getPropSafely(KEY_VERSION_SAMSUNG))) {
            currentROM = ROM_SAMSUNG;
        } else {
            String sVersion = Build.DISPLAY;
            if (sVersion.toUpperCase().contains(ROM_FLYME)) {
                currentROM = ROM_FLYME;
            } else {
                currentROM = Build.MANUFACTURER.toUpperCase();
            }
        }

        sDetectedROM.set(currentROM);
        return currentROM.equals(rom);
    }

    // ========== Cleanup and Resource Management ==========

    /**
     * Clean up all resources and caches
     */
    public static void cleanup() {
        cleanup(null);
    }

    /**
     * Clean up all resources and caches with context for screenshot detection
     */
    public static void cleanup(Context context) {
        try {
            Log.d(TAG, "Cleaning up RecorderFakeUtils resources");

            // Stop screenshot detection first
            if (sIsScreenshotDetectionActive.get()) {
                stopScreenshotDetection(context);
            }

            // Clear screenshot detection state
            cleanupScreenshotDetectionState();

            // Clear all caches
            sPropCache.clear();
            sFieldCache.clear();
            sMethodCache.clear();
            sConstructorCache.clear();

            // Clear view references
            sViewRefs.clear();

            // Reset state
            sInitialized.set(false);
            sDetectedROM.set(null);
            sIsProcessing.set(false);
            sLastUpdateTime = 0;

            // Shutdown background executor
            if (!sBackgroundExecutor.isShutdown()) {
                sBackgroundExecutor.shutdown();
                try {
                    if (!sBackgroundExecutor.awaitTermination(1, TimeUnit.SECONDS)) {
                        sBackgroundExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    sBackgroundExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            Log.d(TAG, "RecorderFakeUtils cleanup completed");

        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }

    /**
     * Clean up screenshot detection state
     */
    private static void cleanupScreenshotDetectionState() {
        try {
            // Reset screenshot detection state
            sIsScreenshotDetectionActive.set(false);
            sIsScreenshotInProgress.set(false);
            sIsUIHiddenForScreenshot.set(false);
            sLastScreenshotTime.set(0);
            sScreenshotDetectionStartTime.set(0);

            // Clear hidden views tracking
            sHiddenViews.clear();
            sHiddenViewIds.clear();

            // Clear state tracking maps
            sOriginalVisibilityMap.clear();
            sOriginalAlphaMap.clear();
            sOriginalFlagsMap.clear();

            // Clear callbacks
            sScreenshotCallbacks.clear();

            // Reset observers
            sMediaStoreObserver = null;
            sScreenshotFileObserver = null;

            Log.d(TAG, "Screenshot detection state cleaned up");

        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up screenshot detection state", e);
        }
    }

}
