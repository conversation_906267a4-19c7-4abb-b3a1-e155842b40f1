package com.bearmod;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Optimized LanguageManager with on-demand loading and caching
 * Reduces memory usage and improves startup performance
 */
public class LanguageManager {
    private static final String TAG = "LanguageManager";

    // Singleton instance
    private static volatile LanguageManager instance;

    // Current language cache - only stores the active language
    private final Map<String, String> currentLanguageCache = new ConcurrentHashMap<>();

    // Current language identifier
    private String currentLanguage = "en";

    // Application context for resource access
    private Context context;

    // Language change listeners for UI updates
    private final Map<String, LanguageChangeListener> listeners = new ConcurrentHashMap<>();

    // Available languages
    private static final String[] SUPPORTED_LANGUAGES = {"en", "zh", "ru"};

    // Language file mapping
    private static final Map<String, String> LANGUAGE_FILES = new HashMap<>();

    static {
        LANGUAGE_FILES.put("en", "languages/english.properties");
        LANGUAGE_FILES.put("zh", "languages/chinese.properties");
        LANGUAGE_FILES.put("ru", "languages/russian.properties");
    }

    /**
     * Interface for language change notifications
     */
    public interface LanguageChangeListener {
        void onLanguageChanged(String newLanguage);
    }

    /**
     * Private constructor for singleton pattern
     */
    private LanguageManager() {
        // Initialize with default fallback translations
        initializeFallbackTranslations();
    }

    /**
     * Get singleton instance
     */
    public static LanguageManager getInstance() {
        if (instance == null) {
            synchronized (LanguageManager.class) {
                if (instance == null) {
                    instance = new LanguageManager();
                }
            }
        }
        return instance;
    }

    /**
     * Initialize the LanguageManager with application context
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        // Load the current language on initialization
        loadLanguage(currentLanguage);
    }

    /**
     * Initialize fallback translations for critical keys
     * This ensures the app works even if language files are missing
     */
    private void initializeFallbackTranslations() {
        // Add essential fallback translations
        currentLanguageCache.put("BearMod", "BearMod");
        currentLanguageCache.put("Main Menu", "Main Menu");
        currentLanguageCache.put("Settings", "Settings");
        currentLanguageCache.put("Language", "Language");
    }

    /**
     * Load language from resource file on-demand
     */
    private synchronized void loadLanguage(String languageCode) {
        if (!isLanguageSupported(languageCode)) {
            Log.w(TAG, "Unsupported language: " + languageCode + ", falling back to English");
            languageCode = "en";
        }

        // Clear current cache
        currentLanguageCache.clear();

        // Re-initialize fallback translations
        initializeFallbackTranslations();

        // Load from assets if context is available
        if (context != null) {
            loadLanguageFromAssets(languageCode);
        } else {
            // Load hardcoded fallback for the requested language
            loadHardcodedFallback(languageCode);
        }

        currentLanguage = languageCode;
        Log.d(TAG, "Language loaded: " + languageCode + " with " + currentLanguageCache.size() + " translations");
    }

    /**
     * Load language from assets folder
     */
    private void loadLanguageFromAssets(String languageCode) {
        String fileName = LANGUAGE_FILES.get(languageCode);
        if (fileName == null) {
            Log.w(TAG, "No language file defined for: " + languageCode);
            return;
        }

        try {
            AssetManager assetManager = context.getAssets();
            InputStream inputStream = assetManager.open(fileName);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue; // Skip empty lines and comments
                }

                int separatorIndex = line.indexOf('=');
                if (separatorIndex > 0) {
                    String key = line.substring(0, separatorIndex).trim();
                    String value = line.substring(separatorIndex + 1).trim();
                    currentLanguageCache.put(key, value);
                }
            }

            reader.close();
            inputStream.close();

        } catch (IOException e) {
            Log.e(TAG, "Failed to load language file: " + fileName, e);
            // Fall back to hardcoded translations
            loadHardcodedFallback(languageCode);
        }
    }

    /**
     * Load hardcoded fallback translations
     * This ensures the app works even without asset files
     */
    private void loadHardcodedFallback(String languageCode) {
        switch (languageCode) {
            case "zh":
                loadChineseFallback();
                break;
            case "ru":
                loadRussianFallback();
                break;
            default:
                loadEnglishFallback();
                break;
        }
    }

    /**
     * Load essential English translations
     */
    private void loadEnglishFallback() {
        currentLanguageCache.put("livestream_mode", "LiveStreamMode (Recording Hide)");
        currentLanguageCache.put("Weapon", "Weapon");
        currentLanguageCache.put("Grenade Warning", "Grenade Warning");
        currentLanguageCache.put("360° Alert", "360° Alert");
        currentLanguageCache.put("Radar MAP", "Radar MAP");
        currentLanguageCache.put("IgnoreBot-ESP", "IgnoreBot-ESP");
        currentLanguageCache.put("Aimbot", "Aimbot");
        currentLanguageCache.put("Auto Fire", "Auto Fire");
        currentLanguageCache.put("Hide ESP", "Hide ESP");
        currentLanguageCache.put("Hide Menu", "Hide Menu");
        currentLanguageCache.put("Esp Menu", "Esp Menu");
        currentLanguageCache.put("Aim Menu", "Aim Menu");
        currentLanguageCache.put("Skin Menu", "Skin Menu");
        currentLanguageCache.put("Line", "Line");
        currentLanguageCache.put("Bone", "Bone");
        currentLanguageCache.put("Info", "Info");
        currentLanguageCache.put("AimBot", "AimBot");
        currentLanguageCache.put("Head", "Head");
        currentLanguageCache.put("Body", "Body");
        currentLanguageCache.put("Skin-Enable", "Skin-Enable");
    }

    /**
     * Load essential Chinese translations
     */
    private void loadChineseFallback() {
        currentLanguageCache.put("livestream_mode", "直播模式（录制隐藏）");
        currentLanguageCache.put("Weapon", "武器");
        currentLanguageCache.put("Grenade Warning", "手雷警告");
        currentLanguageCache.put("360° Alert", "360°警报");
        currentLanguageCache.put("Radar MAP", "雷达地图");
        currentLanguageCache.put("IgnoreBot-ESP", "忽略机器人");
        currentLanguageCache.put("Aimbot", "自瞄");
        currentLanguageCache.put("Auto Fire", "自动开火");
        currentLanguageCache.put("Hide ESP", "隐藏ESP");
        currentLanguageCache.put("Hide Menu", "隐藏菜单");
        currentLanguageCache.put("Main Menu", "主菜单");
        currentLanguageCache.put("Esp Menu", "ESP菜单");
        currentLanguageCache.put("Aim Menu", "自瞄菜单");
        currentLanguageCache.put("Skin Menu", "皮肤菜单");
        currentLanguageCache.put("Line", "线");
        currentLanguageCache.put("Bone", "骨骼");
        currentLanguageCache.put("Info", "玩家信息");
        currentLanguageCache.put("AimBot", "自瞄");
        currentLanguageCache.put("Head", "头");
        currentLanguageCache.put("Body", "身体");
        currentLanguageCache.put("Skin-Enable", "启用皮肤");
    }

    /**
     * Load essential Russian translations
     */
    private void loadRussianFallback() {
        currentLanguageCache.put("livestream_mode", "Режим трансляции (Скрытие записи)");
        currentLanguageCache.put("Weapon", "Оружие");
        currentLanguageCache.put("Grenade Warning", "Предупреждение о гранате");
        currentLanguageCache.put("360° Alert", "Оповещение 360°");
        currentLanguageCache.put("Radar MAP", "Карта-радар");
        currentLanguageCache.put("IgnoreBot-ESP", "Игнорировать ботов");
        currentLanguageCache.put("Aimbot", "Аимбот");
        currentLanguageCache.put("Auto Fire", "Автовыстрел");
        currentLanguageCache.put("Hide ESP", "Скрыть ESP");
        currentLanguageCache.put("Hide Menu", "Скрыть меню");
        currentLanguageCache.put("Main Menu", "Главное меню");
        currentLanguageCache.put("Esp Menu", "Меню ESP");
        currentLanguageCache.put("Aim Menu", "Меню Аимбота");
        currentLanguageCache.put("Skin Menu", "Меню скинов");
        currentLanguageCache.put("Line", "Линия");
        currentLanguageCache.put("Bone", "Кость");
        currentLanguageCache.put("Info", "Информация игрока");
        currentLanguageCache.put("AimBot", "Аимбот");
        currentLanguageCache.put("Head", "Голова");
        currentLanguageCache.put("Body", "Тело");
        currentLanguageCache.put("Skin-Enable", "Включить скины");
    }

    /**
     * Check if a language is supported
     */
    private boolean isLanguageSupported(String languageCode) {
        for (String supported : SUPPORTED_LANGUAGES) {
            if (supported.equals(languageCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Set the current language and load it on-demand
     * This method triggers UI refresh notifications
     */
    public void setLanguage(String languageCode) {
        if (!languageCode.equals(currentLanguage)) {
            loadLanguage(languageCode);
            notifyLanguageChanged(languageCode);
        }
    }

    /**
     * Set language by index (for backward compatibility with Floating.java)
     */
    public void setLanguage(int languageIndex) {
        String languageCode;
        switch (languageIndex) {
            case 1:
                languageCode = "zh";
                break;
            case 2:
                languageCode = "ru";
                break;
            default:
                languageCode = "en";
                break;
        }
        setLanguage(languageCode);
    }

    /**
     * Get translation for a key (backward compatible with existing code)
     */
    public String get(String key) {
        return currentLanguageCache.getOrDefault(key, key);
    }

    /**
     * Get text with fallback support (for Floating.java compatibility)
     */
    public String getText(String englishText, String chineseText, int languageIndex) {
        switch (languageIndex) {
            case 1: // Chinese
                return chineseText;
            default: // English or other
                return englishText;
        }
    }

    /**
     * Get current language code
     */
    public String getCurrentLanguage() {
        return currentLanguage;
    }

    /**
     * Get current language index (for backward compatibility)
     */
    public int getCurrentLanguageIndex() {
        switch (currentLanguage) {
            case "zh":
                return 1;
            case "ru":
                return 2;
            default:
                return 0;
        }
    }

    /**
     * Get list of supported languages
     */
    public String[] getSupportedLanguages() {
        return SUPPORTED_LANGUAGES.clone();
    }

    /**
     * Add a language change listener
     */
    public void addLanguageChangeListener(String id, LanguageChangeListener listener) {
        listeners.put(id, listener);
    }

    /**
     * Remove a language change listener
     */
    public void removeLanguageChangeListener(String id) {
        listeners.remove(id);
    }

    /**
     * Notify all listeners about language change
     */
    private void notifyLanguageChanged(String newLanguage) {
        for (LanguageChangeListener listener : listeners.values()) {
            try {
                listener.onLanguageChanged(newLanguage);
            } catch (Exception e) {
                Log.e(TAG, "Error notifying language change listener", e);
            }
        }
    }

    /**
     * Clear cache and reload current language
     * Useful for refreshing translations after updates
     */
    public void refresh() {
        loadLanguage(currentLanguage);
        notifyLanguageChanged(currentLanguage);
    }

    /**
     * Get cache size for debugging
     */
    public int getCacheSize() {
        return currentLanguageCache.size();
    }

    // Static methods for backward compatibility with existing code
    // Removed duplicate static methods to fix compilation errors
    // Use getInstance().methodName() instead
}