package com.bearmod;

import java.util.HashMap;
import java.util.Map;

public class LanguageManager {
    private static Map<String, Map<String, String>> languages = new HashMap<>();
    private static String currentLanguage = "en";

    static {
        // English
        Map<String, String> en = new HashMap<>();
        en.put("livestream_mode", "LiveStreamMode (Recording Hide)");
        en.put("Weapon", "Weapon");
        en.put("Grenade Warning", "Grenade Warning");
        en.put("360° Alert", "360° Alert");
        en.put("Radar MAP", "Radar MAP");
        en.put("IgnoreBot-ESP", "IgnoreBot-ESP");
        en.put("RadarMAP-Size", "RadarMAP-Size");
        en.put("Aimbot", "Aimbot");
        en.put("Auto Fire", "Auto Fire");
        en.put("Aimbot FOV", "Aimbot FOV");
        en.put("Aimbot Smoothness", "Aimbot Smoothness");
        en.put("Unlock All Skins", "Unlock All Skins");
        en.put("Unlock All Weapons", "Unlock All Weapons");
        en.put("Hide ESP", "Hide ESP");
        en.put("Hide Menu", "Hide Menu");
        en.put("Main Menu", "Main Menu");
        en.put("Esp Menu", "Esp Menu");
        en.put("Aim Menu", "Aim Menu");
        en.put("Skin Menu", "Skin Menu");
        en.put("Hide Menu", "Hide Menu");
        en.put("Line", "Line");
        en.put("Bone", "Bone");
        en.put("Info", "Info");
        en.put("AimBot", "AimBot");
        en.put("Aim-Target", "Aim-Target");
        en.put("Head", "Head");
        en.put("Body", "Body");
        en.put("Aim-Trigger", "Aim-Trigger");
        en.put("Shoot", "Shoot");
        en.put("Scope", "Scope");
        en.put("Both", "Both");
        en.put("Aim-IgnoreBot", "Aim-IgnoreBot");
        en.put("Aim-Knocked", "Aim-Knocked");
        en.put("iPad View", "iPad View");
        en.put("VisiCheck", "VisiCheck");
        en.put("Aim-Recoil", "Aim-Recoil");
        en.put("RecoilSize", "RecoilSize");
        en.put("Aim-Dist", "Aim-Dist");
        en.put("Fov-Aim", "Fov-Aim");
        en.put("update_available", "Update Available!");
        en.put("Skin-Enable", "Skin-Enable");
        en.put("DeadBox (Ingame for open)", "DeadBox (Ingame for open)");
        en.put("X-suit", "X-suit");
        en.put("Set", "Set");
        en.put("Skin-BackPack", "Skin-BackPack");
        en.put("Skin-Helmet", "Skin-Helmet");
        en.put("M416", "M416");
        en.put("AKM", "AKM");
        en.put("SCAR-L", "SCAR-L");
        en.put("M762", "M762");
        en.put("M16A4", "M16A4");
        en.put("GROZAR", "GROZAR");
        en.put("AUG", "AUG");
        en.put("ACE32", "ACE32");
        en.put("M249", "M249");
        en.put("DP28", "DP28");
        en.put("MG3", "MG3");
        en.put("P90", "P90");
        en.put("UZI", "UZI");
        en.put("UMP45", "UMP45");
        en.put("VECTOR", "VECTOR");
        en.put("THOMPSON", "THOMPSON");
        en.put("M24", "M24");
        en.put("KAR98K", "KAR98K");
        en.put("AWM", "AWM");
        en.put("AMR", "AMR");
        en.put("MK14", "MK14");
        en.put("Dacia", "Dacia");
        en.put("CoupeRP", "CoupeRP");
        en.put("UAZ", "UAZ");
        en.put("Moto", "Moto");
        languages.put("en", en);

        // Chinese
        Map<String, String> zh = new HashMap<>();
        zh.put("livestream_mode", "直播模式（录制隐藏）");
        zh.put("Weapon", "武器");
        zh.put("Grenade Warning", "手雷警告");
        zh.put("360° Alert", "360°警报");
        zh.put("Radar MAP", "雷达地图");
        zh.put("IgnoreBot-ESP", "忽略机器人");
        zh.put("RadarMAP-Size", "雷达地图大小");
        zh.put("Aimbot", "自瞄");
        zh.put("Auto Fire", "自动开火");
        zh.put("Aimbot FOV", "自瞄范围");
        zh.put("Aimbot Smoothness", "自瞄平滑度");
        zh.put("Unlock All Skins", "解锁所有皮肤");
        zh.put("Unlock All Weapons", "解锁所有武器");
        zh.put("Hide ESP", "隐藏ESP");
        zh.put("Hide Menu", "隐藏菜单");
        zh.put("Main Menu", "主菜单");
        zh.put("Esp Menu", "ESP菜单");
        zh.put("Aim Menu", "自瞄菜单");
        zh.put("Skin Menu", "皮肤菜单");
        zh.put("Hide Menu", "隐藏菜单");
        zh.put("Line", "线");
        zh.put("Bone", "骨骼");
        zh.put("Info", "玩家信息");
        zh.put("AimBot", "自瞄");
        zh.put("Aim-Target", "瞄准目标");
        zh.put("Head", "头");
        zh.put("Body", "身体");
        zh.put("Aim-Trigger", "瞄准扳机");
        zh.put("Shoot", "射击");
        zh.put("Scope", "开镜");
        zh.put("Both", "两个都");
        zh.put("Aim-IgnoreBot", "瞄准-忽略机器人");
        zh.put("Aim-Knocked", "瞄准击倒");
        zh.put("iPad View", "iPad 视图");
        zh.put("VisiCheck", "掩体预判");
        zh.put("Aim-Recoil", "自动瞄准控制后坐力");
        zh.put("RecoilSize", "自瞄控制后坐力大小");
        zh.put("Aim-Dist", "瞄准距离");
        zh.put("Fov-Aim", "视野瞄准");
        zh.put("update_available", "有更新!");
        zh.put("Skin-Enable", "启用皮肤");
        zh.put("DeadBox (Ingame for open)", "死盒（游戏内开启）");
        zh.put("X-suit", "X套装");
        zh.put("Set", "套装");
        zh.put("Skin-BackPack", "背包皮肤");
        zh.put("Skin-Helmet", "头盔皮肤");
        zh.put("M416", "M416");
        zh.put("AKM", "AKM");
        zh.put("SCAR-L", "SCAR-L");
        zh.put("M762", "M762");
        zh.put("M16A4", "M16A4");
        zh.put("GROZAR", "GROZAR");
        zh.put("AUG", "AUG");
        zh.put("ACE32", "ACE32");
        zh.put("M249", "M249");
        zh.put("DP28", "DP28");
        zh.put("MG3", "MG3");
        zh.put("P90", "P90");
        zh.put("UZI", "UZI");
        zh.put("UMP45", "UMP45");
        zh.put("VECTOR", "VECTOR");
        zh.put("THOMPSON", "THOMPSON");
        zh.put("M24", "M24");
        zh.put("KAR98K", "KAR98K");
        zh.put("AWM", "AWM");
        zh.put("AMR", "AMR");
        zh.put("MK14", "MK14");
        zh.put("Dacia", "Dacia");
        zh.put("CoupeRP", "CoupeRP");
        zh.put("UAZ", "UAZ");
        zh.put("Moto", "Moto");
        languages.put("zh", zh);

        // Russian
        Map<String, String> ru = new HashMap<>();
        ru.put("livestream_mode", "Режим трансляции (Скрытие записи)");
        ru.put("Weapon", "Оружие");
        ru.put("Grenade Warning", "Предупреждение о гранате");
        ru.put("360° Alert", "Оповещение 360°");
        ru.put("Radar MAP", "Карта-радар");
        ru.put("IgnoreBot-ESP", "Игнорировать ботов");
        ru.put("RadarMAP-Size", "Размер карты-радара");
        ru.put("Aimbot", "Аимбот");
        ru.put("Auto Fire", "Автовыстрел");
        ru.put("Aimbot FOV", "Угол обзора Аимбота");
        ru.put("Aimbot Smoothness", "Плавность Аимбота");
        ru.put("Unlock All Skins", "Разблокировать все скины");
        ru.put("Unlock All Weapons", "Разблокировать все оружие");
        ru.put("Hide ESP", "Скрыть ESP");
        ru.put("Hide Menu", "Скрыть меню");
        ru.put("Main Menu", "Главное меню");
        ru.put("Esp Menu", "Меню ESP");
        ru.put("Aim Menu", "Меню Аимбота");
        ru.put("Skin Menu", "Меню скинов");
        ru.put("Hide Menu", "Меню скрытия");
        ru.put("Line", "Линия");
        ru.put("Bone", "Кость");
        ru.put("Info", "Информация игрока");
        ru.put("AimBot", "Аимбот");
        ru.put("Aim-Target", "Цель Аимбота");
        ru.put("Head", "Голова");
        ru.put("Body", "Тело");
        ru.put("Aim-Trigger", "Триггер Аимбота");
        ru.put("Shoot", "Стрелять");
        ru.put("Scope", "Прицел");
        ru.put("Both", "Оба");
        ru.put("Aim-IgnoreBot", "Аимбот - Игнорировать ботов");
        ru.put("Aim-Knocked", "Аимбот - Сбитый");
        ru.put("iPad View", "Вид iPad");
        ru.put("VisiCheck", "Проверка видимости");
        ru.put("Aim-Recoil", "Аимбот - Контроль отдачи");
        ru.put("RecoilSize", "Размер отдачи");
        ru.put("Aim-Dist", "Дистанция Аимбота");
        ru.put("Fov-Aim", "Угол обзора Аимбота");
        ru.put("update_available", "Доступно обновление!");
        ru.put("Skin-Enable", "Включить скины");
        ru.put("DeadBox (Ingame for open)", "Мертвый ящик (в игре для открытия)");
        ru.put("X-suit", "X-костюм");
        ru.put("Set", "Комплект");
        ru.put("Skin-BackPack", "Скин рюкзака");
        ru.put("Skin-Helmet", "Скин шлема");
        ru.put("M416", "M416");
        ru.put("AKM", "AKM");
        ru.put("SCAR-L", "SCAR-L");
        ru.put("M762", "M762");
        ru.put("M16A4", "M16A4");
        ru.put("GROZAR", "GROZAR");
        ru.put("AUG", "AUG");
        ru.put("ACE32", "ACE32");
        ru.put("M249", "M249");
        ru.put("DP28", "DP28");
        ru.put("MG3", "MG3");
        ru.put("P90", "P90");
        ru.put("UZI", "UZI");
        ru.put("UMP45", "UMP45");
        ru.put("VECTOR", "VECTOR");
        ru.put("THOMPSON", "THOMPSON");
        ru.put("M24", "M24");
        ru.put("KAR98K", "KAR98K");
        ru.put("AWM", "AWM");
        ru.put("AMR", "AMR");
        ru.put("MK14", "MK14");
        ru.put("Dacia", "Dacia");
        ru.put("CoupeRP", "CoupeRP");
        ru.put("UAZ", "UAZ");
        ru.put("Moto", "Moto");
        languages.put("ru", ru);
    }

    public static void setLanguage(String lang) {
        if (languages.containsKey(lang)) {
            currentLanguage = lang;
        }
    }

    public static String get(String key) {
        return languages.get(currentLanguage).getOrDefault(key, key);
    }

    public static String getCurrentLanguage() {
        return currentLanguage;
    }
} 